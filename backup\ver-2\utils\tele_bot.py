from telegram import Bot
import asyncio

import warnings
# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# It's good practice to not hardcode tokens.
# Consider using environment variables or a config file for security.
TELEGRAM_BOT_TOKEN = '**********************************************'
 
# Define a default chat ID if you frequently send messages to the same chat.
# You can override this by passing a different chat_id to the function.
DEFAULT_CHAT_ID = '-1002707248470'
 
async def send_telegram_message(message_text: str, target_chat_id: str = DEFAULT_CHAT_ID):
    """
    Sends a text message to a specified Telegram chat.
 
    This function initializes a Telegram Bot object, uses an async context manager
    to ensure proper connection handling, and sends the provided message text
    to the target chat ID.
 
    Args:
        message_text (str): The text content of the message to send.
        target_chat_id (str): The ID of the chat where the message will be sent.
                              Defaults to DEFAULT_CHAT_ID.
    """
    bot = Bot(token=TELEGRAM_BOT_TOKEN)
    # The `async with bot:` context manager handles bot.initialize() and bot.shutdown()
    # for all operations within this block. This is more efficient than
    # initializing/shutting down the bot for every single message.
    async with bot:
        try:
            await bot.send_message(chat_id=target_chat_id, text=message_text)
            print(f"Message successfully sent to chat ID: {target_chat_id}")
        except Exception as e:
            print(f"Error sending message to chat ID {target_chat_id}: {e}")
 
# --- How to use this function in your Jupyter/IPython environment ---
 
# You can now call `send_telegram_message` directly using `await` in any cell.
 
# Example 1: Sending the original test message to the default chat ID
# (You would paste this into a new cell and run it)
#
# test_messages_content = """
# 🙏 ಆತ್ಮನಿರ್ಭರ ಗುಂಪಿಗೆ ಎಲ್ಲರಿಗೂ ಹೃತ್ಪೂರ್ವಕ ಸ್ವಾಗತ!
#
# 🌟 ಇಲ್ಲಿ ನಾವು ಜ್ಞಾನವನ್ನು ಹಂಚಿಕೊಳ್ಳುತ್ತೇವೆ, ಪರಸ್ಪರ ಬೆಳೆದುಕೊಳ್ಳುತ್ತೇವೆ ಮತ್ತು ಯಶಸ್ಸಿನತ್ತ ಹೆಜ್ಜೆ ಇಡುತ್ತೇವೆ.
#
# 📢 ದಯವಿಟ್ಟು ಗುಂಪಿನ ನಿಯಮಗಳನ್ನು ಪಾಲಿಸಿ ಮತ್ತು ಸಕ್ರಿಯವಾಗಿ ಭಾಗವಹಿಸಿ.
#
# 💬 ನಿಮ್ಮ ಸಲಹೆ, ಅನುಭವ ಅಥವಾ ಪ್ರಶ್ನೆಗಳನ್ನು ಹಂಚಿಕೊಳ್ಳಿ – ಪ್ರತಿಯೊಬ್ಬ ಸದಸ್ಯನ ಅಭಿಪ್ರಾಯವು ಮುಖ್ಯವಾಗಿದೆ!
#
# 🚀 ಬನ್ನಿ, ಈ ಪ್ರಯಾಣವನ್ನು ಒಟ್ಟಾಗಿ ಸ್ಫೂರ್ತಿದಾಯಕವಾಗಿ ರೂಪಿಸೋಣ!
# """
#
# await send_telegram_message(test_messages_content)
 
# Example 2: Sending a simple message to the default chat ID
# (You would paste this into a new cell and run it)
#
# await send_telegram_message("Hello from my reusable Telegram sender function!")
 
# Example 3: Sending a message to a different chat ID (if you have one)
# (You would paste this into a new cell and run it)
#
# # Replace 'YOUR_OTHER_CHAT_ID' with the actual ID of another chat
# await send_telegram_message("This message goes to a different group.", "YOUR_OTHER_CHAT_ID")
 
# --- Important Note ---
# The original `async def main():` function and the `await main()` call are no longer needed
# as `send_telegram_message` is now your reusable function.
# You should remove them from your notebook cell.