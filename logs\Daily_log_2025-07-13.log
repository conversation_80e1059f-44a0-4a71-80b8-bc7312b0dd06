2025-07-13 21:09:08 INFO: === **************** BK EXCLUSIVE ************************ ===
2025-07-13 21:09:08 INFO: === ****************START LOGGING* (21:09:08) ************ ===
2025-07-13 21:09:08 INFO: === ****************************************************** ===
2025-07-13 21:09:08 INFO: === Algo Trading Bot Started ===
2025-07-13 21:09:08 INFO: Output for processed NIFTY data: processed_files
2025-07-13 21:09:08 INFO: Output for processed Options data: processed_options_files
2025-07-13 21:09:08 INFO: Options input file: User_options_input.csv
2025-07-13 21:09:08 INFO: ============================================================
2025-07-13 21:09:08 DEBUG: Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time> 
 Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30
2025-07-13 21:09:08 INFO: --- Processing Historical NIFTY Data (21:09:08) ---
2025-07-13 21:09:08 INFO:  First run: scheduling all intervals for full historical fetch
2025-07-13 21:09:08 INFO:  Fetching 13 5min data...
2025-07-13 21:09:08 DEBUG: Starting new HTTPS connection (1): api.dhan.co:443
