import pandas as pd
import pandas_ta as ta
import numpy as np
import warnings
import logging

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

def calculate_wma(df, price_column='close', periods=[5, 10, 45, 65, 90]):
    """
    Calculate Weighted Moving Averagess for specified periods using pandas_ta.

    Args:
        df (pd.DataFrame): Input dataframe with price data
        price_column (str): Column name to use for WMA calculation (default: 'close')
        periods (list): List of periods for WMA calculation

    Returns:
        pd.DataFrame: DataFrame with added WMA columns
    """
    df_copy = df.copy()

    # Ensure the price column exists
    if price_column not in df_copy.columns:
        #print(f"  ✗ Price column '{price_column}' not found in dataframe")
        # Add dummy columns to avoid errors later, though processing will be invalid
        for period in periods:
             df_copy[f'WMA{period}'] = np.nan
        return df_copy

    # Calculate WMA for each period
    for period in periods:
        try:
            # Use pandas_ta to calculate WMA
            wma_values = ta.wma(df_copy[price_column], length=period).round(2)
            df_copy[f'WMA{period}'] = wma_values
            # print(f"  ✓ Calculated WMA{period}") # Keep prints for clarity during execution
        except Exception as e:
            print(f"  ✗ Error calculating WMA{period}: {str(e)}")
            df_copy[f'WMA{period}'] = np.nan

    return df_copy


def calculate_rsi_atr(df, rsi_length=14, atr_length=14, price_cols=None):
    """
    Calculate RSI and ATR indicators and add them to the DataFrame.

    Args:
        df (pd.DataFrame): Input dataframe with price data.
        rsi_length (int): Period for RSI calculation (default: 14).
        atr_length (int): Period for ATR calculation (default: 14).
        price_cols (dict or None): Optional mapping for price columns, e.g.,
            {'close': 'Close', 'high': 'High', 'low': 'Low'}
            If None, uses 'close', 'high', 'low' as column names.

    Returns:
        pd.DataFrame: DataFrame with added 'RSI_14' and 'ATR_14' columns.
    """
    df_copy = df.copy()
    # Set default column names or use provided mapping
    close_col = price_cols['close'] if price_cols and 'close' in price_cols else 'close'
    high_col = price_cols['high'] if price_cols and 'high' in price_cols else 'high'
    low_col = price_cols['low'] if price_cols and 'low' in price_cols else 'low'

    # Check if required columns exist
    if close_col in df_copy.columns:
        try:
            df_copy[f'RSI_{rsi_length}'] = ta.rsi(df_copy[close_col], length=rsi_length).round(2)
        except Exception as e:
            print(f"  ✗ Error calculating RSI({rsi_length}): {str(e)}")
            df_copy[f'RSI_{rsi_length}'] = np.nan
    else:
        df_copy[f'RSI_{rsi_length}'] = np.nan

    if all(col in df_copy.columns for col in [high_col, low_col, close_col]):
        try:
            df_copy[f'ATR_{atr_length}'] = ta.atr(
                df_copy[high_col], df_copy[low_col], df_copy[close_col], length=atr_length
            ).round(2)
        except Exception as e:
            print(f"  ✗ Error calculating ATR({atr_length}): {str(e)}")
            df_copy[f'ATR_{atr_length}'] = np.nan
    else:
        df_copy[f'ATR_{atr_length}'] = np.nan

    return df_copy


def detect_crossovers(df, fast_wma='WMA5', slow_wma='WMA10'):
    """
    Detect crossovers between two WMA series.

    Args:
        df (pd.DataFrame): DataFrame containing WMA columns
        fast_wma (str): Column name for fast WMA (default: 'WMA5')
        slow_wma (str): Column name for slow WMA (default: 'WMA10')

    Returns:
        pd.DataFrame: DataFrame with added crossover signal column
    """
    df_copy = df.copy()

    # Use standard column name for WMA5/WMA10 crossovers, custom name for others
    if fast_wma == 'WMA5' and slow_wma == 'WMA10':
        crossover_signal_name = 'crossover_signal'
    else:
        crossover_signal_name = f'crossover_signal_{fast_wma}_{slow_wma}'

    # Check if required columns exist
    if fast_wma not in df_copy.columns or slow_wma not in df_copy.columns:
        #print(f"  ✗ Required WMA columns not found for crossover detection")
        df_copy[crossover_signal_name] = 'neutral'
        return df_copy

    # Initialize crossover signal column
    df_copy[crossover_signal_name] = 'neutral'

    # Calculate crossover signals
    try:
        # Get the WMA series
        fast_series = df_copy[fast_wma]
        slow_series = df_copy[slow_wma]

        # Calculate the difference and its shift
        diff = fast_series - slow_series
        diff_prev = diff.shift(1)

        # Detect crossovers
        # Upward crossover: fast WMA crosses above slow WMA
        upward_cross = (diff > 0) & (diff_prev <= 0)

        # Downward crossover: fast WMA crosses below slow WMA
        downward_cross = (diff < 0) & (diff_prev >= 0)

        # Apply signals
        df_copy.loc[upward_cross, crossover_signal_name] = 'upward'
        df_copy.loc[downward_cross, crossover_signal_name] = 'downward'

        # Count crossovers
        upward_count = upward_cross.sum()
        downward_count = downward_cross.sum()

        # print(f"  ✓ Detected {upward_count} upward and {downward_count} downward crossovers") # Keep prints

    except Exception as e:
        print(f"  ✗ Error detecting crossovers: {str(e)}")

    return df_copy


def detect_supply_demand_zones(df, crossover_column='crossover_signal'):
    """
    Detect supply and demand zones based on crossover transitions.

    Supply Zones: Price ranges between 'upward' → 'downward' crossover transitions
    - Captures the candle with highest 'high' price in the period

    Demand Zones: Price ranges between 'downward' → 'upward' crossover transitions
    - Captures the candle with lowest 'low' price in the period

    Args:
        df (pd.DataFrame): DataFrame containing crossover signals and price data
        crossover_column (str): Column name containing crossover signals

    Returns:
        pd.DataFrame: DataFrame with added supply_zone and demand_zone columns
    """
    df_copy = df.copy()

    # Check if required columns exist
    required_columns = [crossover_column, 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        #print(f"  ✗ Required columns not found for zone detection: {missing_columns}")
        df_copy['Zones_sup_dem'] = ''
        return df_copy

    # Initialize zone columns
    df_copy['Zones_sup_dem'] = ''

    try:
        # Get crossover signals
        signals = df_copy[crossover_column]

        # Find crossover indices
        upward_indices = df_copy[signals == 'upward'].index.tolist()
        downward_indices = df_copy[signals == 'downward'].index.tolist()

        supply_zones_count = 0
        demand_zones_count = 0

        # Detect Supply Zones (upward → downward transitions)
        # Iterate through upward crossovers
        for upward_idx in upward_indices:
            # Find the next downward crossover after this upward crossover
            next_downward_indices = [idx for idx in downward_indices if idx > upward_idx]

            if next_downward_indices:
                # Get the first downward crossover after the current upward one
                downward_idx = next_downward_indices[0]

                # Ensure the range is valid (upward_idx <= downward_idx)
                if upward_idx <= downward_idx:
                    # Find the candle with highest 'high' between upward and the next downward crossover
                    zone_data = df_copy.loc[upward_idx:downward_idx]
                    if not zone_data.empty:
                        max_high_idx = zone_data['high'].idxmax()
                        df_copy.loc[max_high_idx, 'Zones_sup_dem'] = 'Supply'
                        supply_zones_count += 1

        # Detect Demand Zones (downward → upward transitions)
        # Iterate through downward crossovers
        for downward_idx in downward_indices:
            # Find the next upward crossover after this downward crossover
            next_upward_indices = [idx for idx in upward_indices if idx > downward_idx]

            if next_upward_indices:
                # Get the first upward crossover after the current downward one
                upward_idx = next_upward_indices[0]

                # Ensure the range is valid (downward_idx <= upward_idx)
                if downward_idx <= upward_idx:
                    # Find the candle with lowest 'low' between downward and the next upward crossover
                    zone_data = df_copy.loc[downward_idx:upward_idx]
                    if not zone_data.empty:
                        min_low_idx = zone_data['low'].idxmin()
                        df_copy.loc[min_low_idx, 'Zones_sup_dem'] = 'Demand'
                        demand_zones_count += 1

        # print(f"  ✓ Detected {supply_zones_count} supply zones and {demand_zones_count} demand zones") # Keep prints

    except Exception as e:
        print(f"  ✗ Error detecting supply/demand zones: {str(e)}")

    return df_copy


def validate_zones(df):
    """
    Validate supply and demand zones by adding a Zone_Status column.

    Takes a DataFrame with already identified Supply and Demand zones
    (where 'Zones_sup_dem' column contains 'Supply' or 'Demand' values)
    and adds a new column 'Zone_Status' with default value 'Valid' for each identified zone.

    Args:
        df (pd.DataFrame): DataFrame containing identified supply/demand zones

    Returns:
        pd.DataFrame: DataFrame with added Zone_Status column
    """
    df_copy = df.copy()

    # Check if required column exists
    if 'Zones_sup_dem' not in df_copy.columns:
        #print("  ✗ Required column 'Zones_sup_dem' not found for zone validation")
        df_copy['Zone_Status'] = ''
        return df_copy

    try:
        # Initialize Zone_Status column with empty values
        df_copy['Zone_Status'] = ''

        # Set 'Valid' status for identified zones
        zone_mask = (df_copy['Zones_sup_dem'] == 'Supply') | (df_copy['Zones_sup_dem'] == 'Demand')
        df_copy.loc[zone_mask, 'Zone_Status'] = 'Valid'

        # Count validated zones
        supply_zones = (df_copy['Zones_sup_dem'] == 'Supply').sum()
        demand_zones = (df_copy['Zones_sup_dem'] == 'Demand').sum()
        total_zones = supply_zones + demand_zones

        #print(f"  ✓ Validated {total_zones} zones ({supply_zones} supply, {demand_zones} demand)")

    except Exception as e:
        print(f"  ✗ Error validating zones: {str(e)}")
        df_copy['Zone_Status'] = ''

    return df_copy

# def update_supply_zone_status(df):
#     """
#     Update the status of Supply zones based on price action validation.

#     For each Supply zone with 'Valid' status:
#     - Checks if any subsequent candle has a high price greater than the high price of the supply zone
#     - If found, marks the zone as 'Invalid' in the 'Zone_Status' column
#     - If still 'Valid', performs a test check based on crossover signals
#     - If test condition is met, marks the zone as 'Tested' in the 'Zone_Status' column

#     Args:
#         df (pd.DataFrame): DataFrame with validated zones containing 'Zone_Status' column

#     Returns:
#         pd.DataFrame: DataFrame with updated Supply zone statuses
#     """
#     df_copy = df.copy()
#     recent_candle_idx = df_copy.index[-1] if not df_copy.empty else None
#     recent_candle_data = df_copy.iloc[-1]
#     affected_zones = []


#     # Check if required columns exist
#     required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low', 'crossover_signal']
#     missing_columns = [col for col in required_columns if col not in df_copy.columns]

#     if missing_columns:
#         #print(f"  ✗ Required columns not found for supply zone status update: {missing_columns}")
#         return df_copy, pd.DataFrame()

#     try:
#         # Get supply zones with 'Valid' status
#         # supply_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Supply') &
#         #                      (df_copy['Zone_Status'] == 'Valid' or df_copy['Zone_Status'] == 'Tested')]
#         supply_zones = df_copy[((df_copy['Zones_sup_dem'] == 'Supply') & (df_copy['Zone_Status'] == 'Valid'))
#                                | ((df_copy['Zones_sup_dem'] == 'Supply') & (df_copy['Zone_Status'] == 'Tested'))]
#         if supply_zones.empty:
#             #print("  → No valid supply zones found for status update")
#             return df_copy, pd.DataFrame()

#         invalid_count = 0
#         tested_count = 0

#         for zone_idx in supply_zones.index:
#             zone_high = df_copy.loc[zone_idx, 'high']
#             zone_low = df_copy.loc[zone_idx, 'low']
#             zone_crossover = df_copy.loc[zone_idx, 'crossover_signal']

#             # Check subsequent candles (after the zone)
#             subsequent_data = df_copy.loc[zone_idx + 1:]

#             if subsequent_data.empty:
#                 continue

#             # Check for invalidation: any subsequent high > zone high
#             invalidation_check = subsequent_data['high'] > zone_high
#             if invalidation_check.any():
#                 df_copy.loc[zone_idx, 'Zone_Status'] = 'Invalid'
#                 invalid_count += 1
#                 # If most recent candle caused invalidation
#                 # if recent_candle_idx in invalidation_check[invalidation_check].index:
#                 #     affected_zones.append(df_copy.loc[[zone_idx]])
#                 continue

#             # Test check logic
#             test_triggered = False

#             if 'downward' in zone_crossover:
#                 # Zone itself has downward signal - check if any subsequent high >= zone low
#                 test_check = subsequent_data['high'] >= zone_low
#                 if test_check.any():
#                     test_triggered = True
#                     Zone_status_prior_to_changing = df_copy.loc[zone_idx, 'Zone_Status']
#                     if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
#                         df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
#                         affected_zones.append(df_copy.loc[[zone_idx]])
#             else:
#                 # Find next downward crossover after the zone
#                 downward_signals = subsequent_data[subsequent_data['crossover_signal'] == 'downward']
#                 if not downward_signals.empty:
#                     next_downward_idx = downward_signals.index[0]
#                     # Check candles after the next downward signal
#                     after_downward = df_copy.loc[next_downward_idx + 1:]
#                     if not after_downward.empty:
#                         test_check = after_downward['high'] >= zone_low
#                         if test_check.any():
#                             test_triggered = True
#                             Zone_status_prior_to_changing = df_copy.loc[zone_idx, 'Zone_Status']
#                             if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
#                                 df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
#                                 affected_zones.append(df_copy.loc[[zone_idx]])

#             if test_triggered:
#                 df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
#                 tested_count += 1

#         #print(f"  ✓ Updated supply zones: {invalid_count} invalid, {tested_count} tested")

#     except Exception as e:
#         print(f"  ✗ Error updating supply zone status: {str(e)}")

#     result_df = pd.concat(affected_zones) if affected_zones else pd.DataFrame()
#     return df_copy, result_df


# def update_demand_zone_status(df):
#     """
#     Update the status of Demand zones based on price action validation.

#     For each Demand zone with 'Valid' status:
#     - Checks if any subsequent candle has a low price less than the low price of the demand zone
#     - If found, marks the zone as 'Invalid' in the 'Zone_Status' column
#     - If still 'Valid', performs a test check based on crossover signals
#     - If test condition is met, marks the zone as 'Tested' in the 'Zone_Status' column

#     Args:
#         df (pd.DataFrame): DataFrame with validated zones containing 'Zone_Status' column

#     Returns:
#         pd.DataFrame: DataFrame with updated Demand zone statuses
#     """
#     df_copy = df.copy()
#     recent_candle_idx = df_copy.index[-1] if not df_copy.empty else None
#     affected_zones = []

#     # Check if required columns exist
#     required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low', 'crossover_signal']
#     missing_columns = [col for col in required_columns if col not in df_copy.columns]

#     if missing_columns:
#         #print(f"  ✗ Required columns not found for demand zone status update: {missing_columns}")
#         return df_copy, pd.DataFrame()

#     try:
#         # Get demand zones with 'Valid' status
#         #demand_zones = df_copy[(df_copy['Zones_sup_dem'] == 'Demand') &
#         #                      (df_copy['Zone_Status'] == 'Valid' or df_copy['Zone_Status'] == 'Tested')]
#         demand_zones = df_copy[((df_copy['Zones_sup_dem'] == 'Demand') & (df_copy['Zone_Status'] == 'Valid'))
#                                | ((df_copy['Zones_sup_dem'] == 'Demand') & (df_copy['Zone_Status'] == 'Tested'))]
#         if demand_zones.empty:
#             #print("  → No valid demand zones found for status update")
#             return df_copy, pd.DataFrame()

#         invalid_count = 0
#         tested_count = 0

#         for zone_idx in demand_zones.index:
#             zone_high = df_copy.loc[zone_idx, 'high']
#             zone_low = df_copy.loc[zone_idx, 'low']
#             zone_crossover = df_copy.loc[zone_idx, 'crossover_signal']

#             # Check subsequent candles (after the zone)
#             subsequent_data = df_copy.loc[zone_idx + 1:]

#             if subsequent_data.empty:
#                 continue

#             # Check for invalidation: any subsequent low < zone low
#             invalidation_check = subsequent_data['low'] < zone_low
#             if invalidation_check.any():
#                 df_copy.loc[zone_idx, 'Zone_Status'] = 'Invalid'
#                 invalid_count += 1
#                 if recent_candle_idx in invalidation_check[invalidation_check].index:
#                     affected_zones.append(df_copy.loc[[zone_idx]])
#                 continue

#             # Test check logic
#             test_triggered = False

#             if 'upward' in zone_crossover:
#                 # Zone itself has upward signal - check if any subsequent low <= zone high
#                 test_check = subsequent_data['low'] <= zone_high
#                 if test_check.any():
#                     test_triggered = True
#                     Zone_status_prior_to_changing = df_copy.loc[zone_idx, 'Zone_Status']
#                     if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
#                         df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
#                         affected_zones.append(df_copy.loc[[zone_idx]])
#             else:
#                 # Find next upward crossover after the zone
#                 upward_signals = subsequent_data[subsequent_data['crossover_signal'] == 'upward']
#                 if not upward_signals.empty:
#                     next_upward_idx = upward_signals.index[0]
#                     # Check candles after the next upward signal
#                     after_upward = df_copy.loc[next_upward_idx + 1:]
#                     if not after_upward.empty:
#                         test_check = after_upward['low'] <= zone_high
#                         if test_check.any():
#                             test_triggered = True
#                             Zone_status_prior_to_changing = df_copy.loc[zone_idx, 'Zone_Status']
#                             if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
#                                 df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
#                                 affected_zones.append(df_copy.loc[[zone_idx]])

#             if test_triggered:
#                 df_copy.loc[zone_idx, 'Zone_Status'] = 'Tested'
#                 tested_count += 1

#         #print(f"  ✓ Updated demand zones: {invalid_count} invalid, {tested_count} tested")

#     except Exception as e:
#         print(f"  ✗ Error updating demand zone status: {str(e)}")

#     result_df = pd.concat(affected_zones) if affected_zones else pd.DataFrame()
#     return df_copy, result_df

def update_supply_zone_status(df):
    """
    Two-stage update of Supply zone status based on price action validation.

    Stage 1: Process historical data (excluding the most recent candle)
        - Executes zone validation logic on all but the last candle.
        - Updates zone statuses for historical data.

    Stage 2: Process with latest candle
        - Adds the most recent candle to the Stage 1 output.
        - Executes zone validation logic again.
        - Tracks zones updated specifically in Stage 2.

    Args:
        df (pd.DataFrame): DataFrame with validated zones containing 'Zone_Status' column

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]:
            - Fully updated DataFrame (Stage 2)
            - DataFrame of zones modified in Stage 2
    """
    df_copy = df.copy()
    if df_copy.empty:
        return df_copy, pd.DataFrame()

    # --- Stage 1: Process all except the most recent candle ---
    historical_df = df_copy.iloc[:-1].copy()
    recent_candle = df_copy.iloc[[-1]].copy()  # DataFrame with just the last row

    # Run existing logic on historical_df
    try:
        required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low', 'crossover_signal']
        missing_columns = [col for col in required_columns if col not in historical_df.columns]
        if missing_columns:
            return df_copy, pd.DataFrame()

        # Get supply zones with 'Valid' or 'Tested' status
        supply_zones = historical_df[
            ((historical_df['Zones_sup_dem'] == 'Supply') & (historical_df['Zone_Status'] == 'Valid')) |
            ((historical_df['Zones_sup_dem'] == 'Supply') & (historical_df['Zone_Status'] == 'Tested'))
        ]
        invalid_count = 0
        tested_count = 0

        for zone_idx in supply_zones.index:
            zone_high = historical_df.loc[zone_idx, 'high']
            zone_low = historical_df.loc[zone_idx, 'low']
            zone_crossover = historical_df.loc[zone_idx, 'crossover_signal']

            subsequent_data = historical_df.loc[zone_idx + 1:]
            if subsequent_data.empty:
                continue

            # Invalidation: any subsequent high > zone high
            invalidation_check = subsequent_data['high'] > zone_high
            if invalidation_check.any():
                historical_df.loc[zone_idx, 'Zone_Status'] = 'Invalid'
                invalid_count += 1
                continue

            # Test check logic
            test_triggered = False
            if 'downward' in zone_crossover:
                test_check = subsequent_data['high'] >= zone_low
                if test_check.any():
                    test_triggered = True
            else:
                downward_signals = subsequent_data[subsequent_data['crossover_signal'] == 'downward']
                if not downward_signals.empty:
                    next_downward_idx = downward_signals.index[0]
                    after_downward = historical_df.loc[next_downward_idx + 1:]
                    if not after_downward.empty:
                        test_check = after_downward['high'] >= zone_low
                        if test_check.any():
                            test_triggered = True

            if test_triggered:
                historical_df.loc[zone_idx, 'Zone_Status'] = 'Tested'
                tested_count += 1

    except Exception as e:
        print(f"  ✗ Error updating supply zone status (Stage 1): {str(e)}")
        return df_copy, pd.DataFrame()

    # --- Stage 2: Process with latest candle ---
    # Combine historical_df and recent_candle
    stage2_df = pd.concat([historical_df, recent_candle]).copy()
    affected_zones_stage2 = []

    try:
        required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low', 'crossover_signal']
        missing_columns = [col for col in required_columns if col not in stage2_df.columns]
        if missing_columns:
            return stage2_df, pd.DataFrame()

        supply_zones = stage2_df[
            ((stage2_df['Zones_sup_dem'] == 'Supply') & (stage2_df['Zone_Status'] == 'Valid')) |
            ((stage2_df['Zones_sup_dem'] == 'Supply') & (stage2_df['Zone_Status'] == 'Tested'))
        ]
        recent_candle_idx = stage2_df.index[-1]

        for zone_idx in supply_zones.index:
            zone_high = stage2_df.loc[zone_idx, 'high']
            zone_low = stage2_df.loc[zone_idx, 'low']
            zone_crossover = stage2_df.loc[zone_idx, 'crossover_signal']

            subsequent_data = stage2_df.loc[zone_idx + 1:]
            if subsequent_data.empty:
                continue

            # Invalidation: any subsequent high > zone high
            invalidation_check = subsequent_data['high'] > zone_high
            if invalidation_check.any():
                stage2_df.loc[zone_idx, 'Zone_Status'] = 'Invalid'
                # Only track if recent candle caused invalidation
                if recent_candle_idx in invalidation_check[invalidation_check].index:
                    affected_zones_stage2.append(stage2_df.loc[[zone_idx]])
                continue

            # Test check logic
            test_triggered = False
            if 'downward' in zone_crossover:
                test_check = subsequent_data['high'] >= zone_low
                if test_check.any():
                    test_triggered = True
                    Zone_status_prior_to_changing = stage2_df.loc[zone_idx, 'Zone_Status']
                    if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
                        stage2_df.loc[zone_idx, 'Zone_Status'] = 'Tested'
                        affected_zones_stage2.append(stage2_df.loc[[zone_idx]])
            else:
                downward_signals = subsequent_data[subsequent_data['crossover_signal'] == 'downward']
                if not downward_signals.empty:
                    next_downward_idx = downward_signals.index[0]
                    after_downward = stage2_df.loc[next_downward_idx + 1:]
                    if not after_downward.empty:
                        test_check = after_downward['high'] >= zone_low
                        if test_check.any():
                            test_triggered = True
                            Zone_status_prior_to_changing = stage2_df.loc[zone_idx, 'Zone_Status']
                            if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
                                stage2_df.loc[zone_idx, 'Zone_Status'] = 'Tested'
                                affected_zones_stage2.append(stage2_df.loc[[zone_idx]])

            if test_triggered and not (
                'downward' in zone_crossover and
                recent_candle_idx in test_check[test_check].index and
                Zone_status_prior_to_changing == 'Valid'
            ):
                stage2_df.loc[zone_idx, 'Zone_Status'] = 'Tested'

    except Exception as e:
        print(f"  ✗ Error updating supply zone status (Stage 2): {str(e)}")

    # Concatenate affected zones for Stage 2
    result_df_stage2 = pd.concat(affected_zones_stage2) if affected_zones_stage2 else pd.DataFrame()
    return stage2_df, result_df_stage2


def update_demand_zone_status(df):
    """
    Two-stage update of Demand zone status based on price action validation.

    Stage 1: Process historical data (excluding the most recent candle)
        - Executes zone validation logic on all but the last candle.
        - Updates zone statuses for historical data.

    Stage 2: Process with latest candle
        - Adds the most recent candle to the Stage 1 output.
        - Executes zone validation logic again.
        - Tracks zones updated specifically in Stage 2.

    Args:
        df (pd.DataFrame): DataFrame with validated zones containing 'Zone_Status' column

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: 
            - Fully updated DataFrame (Stage 2)
            - DataFrame of zones modified in Stage 2
    """
    df_copy = df.copy()
    if df_copy.empty:
        return df_copy, pd.DataFrame()

    # --- Stage 1: Process all except the most recent candle ---
    historical_df = df_copy.iloc[:-1].copy()
    recent_candle = df_copy.iloc[[-1]].copy()  # DataFrame with just the last row

    # Run existing logic on historical_df
    try:
        required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low', 'crossover_signal']
        missing_columns = [col for col in required_columns if col not in historical_df.columns]
        if missing_columns:
            return df_copy, pd.DataFrame()

        # Get demand zones with 'Valid' or 'Tested' status
        demand_zones = historical_df[
            ((historical_df['Zones_sup_dem'] == 'Demand') & (historical_df['Zone_Status'] == 'Valid')) |
            ((historical_df['Zones_sup_dem'] == 'Demand') & (historical_df['Zone_Status'] == 'Tested'))
        ]
        invalid_count = 0
        tested_count = 0

        for zone_idx in demand_zones.index:
            zone_high = historical_df.loc[zone_idx, 'high']
            zone_low = historical_df.loc[zone_idx, 'low']
            zone_crossover = historical_df.loc[zone_idx, 'crossover_signal']

            subsequent_data = historical_df.loc[zone_idx + 1:]
            if subsequent_data.empty:
                continue

            # Invalidation: any subsequent low < zone low
            invalidation_check = subsequent_data['low'] < zone_low
            if invalidation_check.any():
                historical_df.loc[zone_idx, 'Zone_Status'] = 'Invalid'
                invalid_count += 1
                continue

            # Test check logic
            test_triggered = False
            if 'upward' in zone_crossover:
                test_check = subsequent_data['low'] <= zone_high
                if test_check.any():
                    test_triggered = True
            else:
                upward_signals = subsequent_data[subsequent_data['crossover_signal'] == 'upward']
                if not upward_signals.empty:
                    next_upward_idx = upward_signals.index[0]
                    after_upward = historical_df.loc[next_upward_idx + 1:]
                    if not after_upward.empty:
                        test_check = after_upward['low'] <= zone_high
                        if test_check.any():
                            test_triggered = True

            if test_triggered:
                historical_df.loc[zone_idx, 'Zone_Status'] = 'Tested'
                tested_count += 1

    except Exception as e:
        print(f"  ✗ Error updating demand zone status (Stage 1): {str(e)}")
        return df_copy, pd.DataFrame()

    # --- Stage 2: Process with latest candle ---
    # Combine historical_df and recent_candle
    stage2_df = pd.concat([historical_df, recent_candle]).copy()
    affected_zones_stage2 = []

    try:
        required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low', 'crossover_signal']
        missing_columns = [col for col in required_columns if col not in stage2_df.columns]
        if missing_columns:
            return stage2_df, pd.DataFrame()

        demand_zones = stage2_df[
            ((stage2_df['Zones_sup_dem'] == 'Demand') & (stage2_df['Zone_Status'] == 'Valid')) |
            ((stage2_df['Zones_sup_dem'] == 'Demand') & (stage2_df['Zone_Status'] == 'Tested'))
        ]
        recent_candle_idx = stage2_df.index[-1]

        for zone_idx in demand_zones.index:
            zone_high = stage2_df.loc[zone_idx, 'high']
            zone_low = stage2_df.loc[zone_idx, 'low']
            zone_crossover = stage2_df.loc[zone_idx, 'crossover_signal']

            subsequent_data = stage2_df.loc[zone_idx + 1:]
            if subsequent_data.empty:
                continue

            # Invalidation: any subsequent low < zone low
            invalidation_check = subsequent_data['low'] < zone_low
            if invalidation_check.any():
                stage2_df.loc[zone_idx, 'Zone_Status'] = 'Invalid'
                # Only track if recent candle caused invalidation
                if recent_candle_idx in invalidation_check[invalidation_check].index:
                    affected_zones_stage2.append(stage2_df.loc[[zone_idx]])
                continue

            # Test check logic
            test_triggered = False
            if 'upward' in zone_crossover:
                test_check = subsequent_data['low'] <= zone_high
                if test_check.any():
                    test_triggered = True
                    Zone_status_prior_to_changing = stage2_df.loc[zone_idx, 'Zone_Status']
                    if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
                        stage2_df.loc[zone_idx, 'Zone_Status'] = 'Tested'
                        affected_zones_stage2.append(stage2_df.loc[[zone_idx]])
            else:
                upward_signals = subsequent_data[subsequent_data['crossover_signal'] == 'upward']
                if not upward_signals.empty:
                    next_upward_idx = upward_signals.index[0]
                    after_upward = stage2_df.loc[next_upward_idx + 1:]
                    if not after_upward.empty:
                        test_check = after_upward['low'] <= zone_high
                        if test_check.any():
                            test_triggered = True
                            Zone_status_prior_to_changing = stage2_df.loc[zone_idx, 'Zone_Status']
                            if recent_candle_idx in test_check[test_check].index and Zone_status_prior_to_changing == 'Valid':
                                stage2_df.loc[zone_idx, 'Zone_Status'] = 'Tested'
                                affected_zones_stage2.append(stage2_df.loc[[zone_idx]])

            if test_triggered and not (
                'upward' in zone_crossover and
                recent_candle_idx in test_check[test_check].index and
                Zone_status_prior_to_changing == 'Valid'
            ):
                stage2_df.loc[zone_idx, 'Zone_Status'] = 'Tested'

    except Exception as e:
        print(f"  ✗ Error updating demand zone status (Stage 2): {str(e)}")

    # Concatenate affected zones for Stage 2
    result_df_stage2 = pd.concat(affected_zones_stage2) if affected_zones_stage2 else pd.DataFrame()
    return stage2_df, result_df_stage2

def identify_strong_zones(df, crossover_column='crossover_signal'):
    """
    Identify strong trading zones based on WMA relationships and crossover patterns.

    This function marks existing Supply/Demand zones as strong zones based on validation criteria:

    Strong Upward Zone Detection:
    1. For each upward crossover candle, examine the candle immediately preceding it (index - 1)
    2. Verify that the prior candle satisfies: WMA5 < WMA10 < WMA45 < WMA65 < WMA90 (strict ascending order)
    3. If condition #2 is met, examine all subsequent candles starting from the upward crossover candle
       until the next downward crossover (or end of data)
    4. If any candle in this range has a 'high' value >= its WMA45 value, find the most recent
       Demand zone before this upward crossover and mark it as 'strong_upward_zone'

    Strong Downward Zone Detection:
    1. For each downward crossover candle, examine the candle immediately preceding it (index - 1)
    2. Verify that the prior candle satisfies: WMA5 > WMA10 > WMA45 > WMA65 > WMA90 (strict descending order)
    3. If condition #2 is met, examine all subsequent candles starting from the downward crossover candle
       until the next upward crossover (or end of data)
    4. If any candle in this range has a 'low' value <= its WMA45 value, find the most recent
       Supply zone before this downward crossover and mark it as 'strong_downward_zone'

    Args:
        df (pd.DataFrame): DataFrame containing crossover signals, WMA columns, price data, and zones
        crossover_column (str): Column name containing crossover signals

    Returns:
        pd.DataFrame: DataFrame with added strong_zone column
    """
    df_copy = df.copy()

    # Check if required columns exist
    required_columns = [crossover_column, 'high', 'low', 'WMA5', 'WMA10', 'WMA45', 'WMA65', 'WMA90', 'Zones_sup_dem']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]

    if missing_columns:
        #print(f"  ✗ Required columns not found for strong zone detection: {missing_columns}")
        df_copy['strong_zone'] = ''
        return df_copy

    # Initialize strong_zone column with empty strings if not present
    if 'strong_zone' not in df_copy.columns:
        df_copy['strong_zone'] = ''

    try:
        # Get crossover signals
        signals = df_copy[crossover_column]

        # Find crossover indices
        upward_indices = df_copy[signals == 'upward'].index.tolist()
        downward_indices = df_copy[signals == 'downward'].index.tolist()

        # Find existing zone indices
        demand_zones = df_copy[df_copy['Zones_sup_dem'] == 'Demand'].index.tolist()
        supply_zones = df_copy[df_copy['Zones_sup_dem'] == 'Supply'].index.tolist()

        strong_upward_count = 0
        strong_downward_count = 0

        # Process Strong Upward Zones
        for upward_idx in upward_indices:
            # Check if there's a preceding candle
            if upward_idx == 0:
                continue  # Skip first candle as it has no predecessor

            prior_idx = upward_idx - 1

            # Check WMA ordering condition: WMA5 < WMA10 < WMA45 < WMA65 < WMA90 (strict ascending)
            prior_candle = df_copy.loc[prior_idx]

            # Skip if any WMA values are NaN
            wma_values = [prior_candle['WMA5'], prior_candle['WMA10'], prior_candle['WMA45'],
                         prior_candle['WMA65'], prior_candle['WMA90']]
            if any(pd.isna(val) for val in wma_values):
                continue

            if (prior_candle['WMA5'] < prior_candle['WMA10'] < prior_candle['WMA45'] <
                prior_candle['WMA65'] < prior_candle['WMA90']):

                # Find the next downward crossover after this upward crossover
                next_downward_indices = [idx for idx in downward_indices if idx > upward_idx]

                if next_downward_indices:
                    # Examine candles from upward crossover to next downward crossover
                    end_idx = next_downward_indices[0]
                else:
                    # Examine candles from upward crossover to end of data
                    end_idx = df_copy.index[-1]

                # Check if any candle in the range has high >= WMA45
                validation_passed = False
                range_data = df_copy.loc[upward_idx:end_idx]

                for check_idx in range_data.index:
                    candle = df_copy.loc[check_idx]
                    if not pd.isna(candle['WMA45']) and candle['high'] >= candle['WMA45']:
                        # Mark this specific candle with the reason code
                        df_copy.at[check_idx, 'strong_zone'] = 'Reason_for_strong_upward'
                        validation_passed = True
                        break

                # If validation passed, find the most recent Demand zone before this upward crossover
                if validation_passed:
                    recent_demand_zones = [idx for idx in demand_zones if idx < upward_idx]
                    if recent_demand_zones:
                        # Get the most recent (highest index) Demand zone
                        most_recent_demand = max(recent_demand_zones)
                        df_copy.loc[most_recent_demand, 'strong_zone'] = 'strong_upward_zone'
                        strong_upward_count += 1

        # Process Strong Downward Zones
        for downward_idx in downward_indices:
            # Check if there's a preceding candle
            if downward_idx == 0:
                continue  # Skip first candle as it has no predecessor

            prior_idx = downward_idx - 1

            # Check WMA ordering condition: WMA5 > WMA10 > WMA45 > WMA65 > WMA90 (strict descending)
            prior_candle = df_copy.loc[prior_idx]

            # Skip if any WMA values are NaN
            wma_values = [prior_candle['WMA5'], prior_candle['WMA10'], prior_candle['WMA45'],
                         prior_candle['WMA65'], prior_candle['WMA90']]
            if any(pd.isna(val) for val in wma_values):
                continue

            if (prior_candle['WMA5'] > prior_candle['WMA10'] > prior_candle['WMA45'] >
                prior_candle['WMA65'] > prior_candle['WMA90']):

                # Find the next upward crossover after this downward crossover
                next_upward_indices = [idx for idx in upward_indices if idx > downward_idx]

                if next_upward_indices:
                    # Examine candles from downward crossover to next upward crossover
                    end_idx = next_upward_indices[0]
                else:
                    # Examine candles from downward crossover to end of data
                    end_idx = df_copy.index[-1]

                # Check if any candle in the range has low <= WMA45
                validation_passed = False
                range_data = df_copy.loc[downward_idx:end_idx]

                for check_idx in range_data.index:
                    candle = df_copy.loc[check_idx]
                    if not pd.isna(candle['WMA45']) and candle['low'] <= candle['WMA45']:
                        # Mark this specific candle with the reason code
                        df_copy.at[check_idx, 'strong_zone'] = 'Reason_for_Strong_downward'
                        validation_passed = True
                        break

                # If validation passed, find the most recent Supply zone before this downward crossover
                if validation_passed:
                    recent_supply_zones = [idx for idx in supply_zones if idx < downward_idx]
                    if recent_supply_zones:
                        # Get the most recent (highest index) Supply zone
                        most_recent_supply = max(recent_supply_zones)
                        df_copy.loc[most_recent_supply, 'strong_zone'] = 'strong_downward_zone'
                        strong_downward_count += 1

        #print(f"  ✓ Identified {strong_upward_count} strong upward zones and {strong_downward_count} strong downward zones")

    except Exception as e:
        print(f"  ✗ Error identifying strong zones: {str(e)}")
    return df_copy


def find_closest_zones_to_current_candle(df_1, df_5, current_candle):
    """
    Finds the closest trading zones to the current candle from 1-min and 5-min dataframes.
    Returns a DataFrame with all found zones, proximity indicators, and source labels.
    """
    # Helper to process each zone type
    def process_zone(df, zone_col, zone_val, status_col, valid_status, high_or_low, candle_val, proximity_check, source_name):
        # Filter for valid zones
        filtered = df[
            (df[zone_col] == zone_val) &
            (df[status_col].isin(valid_status))
        ]
        if filtered.empty:
            return None
        
        # Find closest zone
        if high_or_low == 'high':
            filtered['abs_diff'] = (filtered['high'] - candle_val).abs()
            closest = filtered.loc[filtered['abs_diff'].idxmin()]
            zone_edge = closest['high']
        else:
            filtered['abs_diff'] = (filtered['low'] - candle_val).abs()
            closest = filtered.loc[filtered['abs_diff'].idxmin()]
            zone_edge = closest['low']
        # Proximity check
        if proximity_check(candle_val, zone_edge):
            proximity = 'Within_zone'
        else:
            proximity = 'outside_zone'
        # Prepare output row
        out_row = closest.drop('abs_diff').to_dict()
        out_row['proximity'] = proximity
        out_row['zone_source'] = source_name
        return out_row

    output_rows = []

    # Upward zones
    # 1-min strong upward
    row = process_zone(
        df_1, 'strong_zone', 'strong_upward_zone', 'Zone_Status', ['Valid', 'Tested'],
        'high', current_candle['low'],
        lambda candle, zone: candle <= zone,
        '1_min_closer_strong_upward_zone'
    )
    if row: output_rows.append(row)

    # 5-min strong upward
    row = process_zone(
        df_5, 'strong_zone', 'strong_upward_zone', 'Zone_Status', ['Valid', 'Tested'],
        'high', current_candle['low'],
        lambda candle, zone: candle <= zone,
        '5_min_closer_strong_upward_zone'
    )
    if row: output_rows.append(row)

    # 1-min demand
    row = process_zone(
        df_1, 'Zones_sup_dem', 'Demand', 'Zone_Status', ['Valid', 'Tested'],
        'high', current_candle['low'],
        lambda candle, zone: candle <= zone,
        '1_min_closer_Demand'
    )
    if row: output_rows.append(row)

    # 5-min demand
    row = process_zone(
        df_5, 'Zones_sup_dem', 'Demand', 'Zone_Status', ['Valid', 'Tested'],
        'high', current_candle['low'],
        lambda candle, zone: candle <= zone,
        '5_min_closer_Demand'
    )
    if row: output_rows.append(row)

    # Downward zones
    # 1-min strong downward
    row = process_zone(
        df_1, 'strong_zone', 'strong_downward_zone', 'Zone_Status', ['Valid', 'Tested'],
        'low', current_candle['high'],
        lambda candle, zone: candle >= zone,
        '1_min_closer_strong_downward_zone'
    )
    if row: output_rows.append(row)

    # 5-min strong downward
    row = process_zone(
        df_5, 'strong_zone', 'strong_downward_zone', 'Zone_Status', ['Valid', 'Tested'],
        'low', current_candle['high'],
        lambda candle, zone: candle >= zone,
        '5_min_closer_strong_downward_zone'
    )
    if row: output_rows.append(row)

    # 1-min supply
    row = process_zone(
        df_1, 'Zones_sup_dem', 'Supply', 'Zone_Status', ['Valid', 'Tested'],
        'low', current_candle['high'],
        lambda candle, zone: candle >= zone,
        '1_min_closer_Supply'
    )
    if row: output_rows.append(row)

    # 5-min supply
    row = process_zone(
        df_5, 'Zones_sup_dem', 'Supply', 'Zone_Status', ['Valid', 'Tested'],
        'low', current_candle['high'],
        lambda candle, zone: candle >= zone,
        '5_min_closer_Supply'
    )
    if row: output_rows.append(row)

    # Return as DataFrame, even if empty
    if output_rows:
        return pd.DataFrame(output_rows)
    else:
        # Return empty DataFrame with expected columns for consistency
        columns = list(df_1.columns) + ['proximity', 'zone_source']
        return pd.DataFrame(columns=columns)

# Example usage:
# result_df = find_closest_zones_to_current_candle(df_1, df_5, current_candle)

def identify_zone_on_zone(df, candle_info, zone_type):
    """
    Detects when a candle intersects with existing supply or demand zones.

    Args:
        df (pd.DataFrame): DataFrame containing zone data (must have 'Zones_sup_dem', 'Zone_Status', 'high', 'low')
        candle_info (dict): Dictionary or object containing candle data with 'high' and 'low' price values
        zone_type (str): Type of zone to check for intersection ("Demand" or "Supply")

    Returns:
        pd.DataFrame: DataFrame containing all matching zone records where the candle intersects the zone.
                      Returns empty DataFrame if no matches found or on error.
    """
    # Validate inputs
    if df is None or df.empty or not isinstance(df, pd.DataFrame):
        return pd.DataFrame()
    # if not isinstance(candle_info, dict) or 'high' not in candle_info or 'low' not in candle_info:
    #     return pd.DataFrame()
    if zone_type not in ['Demand', 'Supply']:
        return pd.DataFrame()
    
    matching_zones = pd.DataFrame()
    
    # Required columns
    required_columns = ['Zones_sup_dem', 'Zone_Status', 'high', 'low']
    if any(col not in df.columns for col in required_columns):
        return pd.DataFrame()
    
    try:
        # Filter zones by type and status
        zone_mask = (
            (df['Zones_sup_dem'] == zone_type) &
            (df['Zone_Status'].isin(['Valid', 'Tested']))
        )
        zones_df = df[zone_mask]

        if zones_df.empty:
            return pd.DataFrame()
        
        # Intersection logic
        if zone_type == "Demand":
            # Candle's low intersects with Demand zone
            intersect_mask = (
                (candle_info['low'] <= zones_df['high']) &
                (candle_info['low'] > zones_df['low'])
            )
        else:  # Supply
            # Candle's high intersects with Supply zone
            intersect_mask = (
                (candle_info['high'] >= zones_df['low']) &
                (candle_info['high'] < zones_df['high'])
            )
            
        matching_zones = zones_df[intersect_mask]

        if matching_zones.empty:
            return pd.DataFrame()
        else:
            return matching_zones

    except Exception as e:
        print(f"  ✗ Error in identify_zone_on_zone: {str(e)}")
        return pd.DataFrame()

def get_resistant_zones(df, price_value, zone_type='Both'):
    """
    Identifies valid supply or demand zones based on a price value.
    
    Args:
        df (pd.DataFrame): DataFrame that has already been processed with supply/demand zones
        price_value (float): Numeric value representing the current price to check against zones
        zone_type (str): Type of zones to find ('Supply', 'Demand', or 'Both')
        
    Returns:
        pd.DataFrame: Filtered DataFrame containing only the rows that meet the conditions
    """
    df_copy = df.copy()
    
    # Check if required columns exist
    required_columns = ['timestamp', 'Zones_sup_dem', 'Zone_Status', 'high', 'low']
    missing_columns = [col for col in required_columns if col not in df_copy.columns]
    
    if missing_columns or df_copy.empty:
        #print(f"  ✗ Required columns not found for resistant zones: {missing_columns}")
        return pd.DataFrame()
    
    try:
        # Initialize empty DataFrame for results
        result_df = pd.DataFrame()
        
        # Filter for Demand zones
        if zone_type in ['Demand', 'Both']:
            demand_zones = df_copy[
                (df_copy['Zones_sup_dem'] == 'Demand') &
                (df_copy['Zone_Status'].isin(['Valid', 'Tested'])) &
                #(df_copy['Zone_Status'].isin(['Valid'])) &
                # (
                #     (
                #         ((price_value - 4) <= df_copy['high']) & (price_value > df_copy['low'])
                #     ) |
                #     (
                #         (price_value <= df_copy['high']) & (price_value > df_copy['low'])
                #     )
                # )
                (price_value <= df_copy['high']) & 
                (price_value > df_copy['low'])
            ]
            result_df = pd.concat([result_df, demand_zones])
            
        # Filter for Supply zones
        if zone_type in ['Supply', 'Both']:
            supply_zones = df_copy[
                (df_copy['Zones_sup_dem'] == 'Supply') &
                (df_copy['Zone_Status'].isin(['Valid', 'Tested'])) &
                #(df_copy['Zone_Status'].isin(['Valid'])) &
                # (
                #     (
                #         ((price_value + 4) >= df_copy['low']) & (price_value < df_copy['high'])
                #     ) |
                #     (
                #         (price_value >= df_copy['low']) & (price_value < df_copy['high'])
                #     )
                # )
                (price_value >= df_copy['low']) & 
                (price_value < df_copy['high'])
            ]
            result_df = pd.concat([result_df, supply_zones])
        
        return result_df
    
    except Exception as e:
        print(f"  ✗ Error identifying resistant zones: {str(e)}")
        return pd.DataFrame()


def process_dataframe(df):
    """
    Processes a DataFrame with WMA calculations, crossover, zone detection, and zone status tracking.

    Args:
        df (pd.DataFrame): Input dataframe with price data.

    Returns:
        pd.DataFrame: Processed DataFrame with zone status tracking.
    """
    if df is None or df.empty:
        #print("  ✗ Input DataFrame is empty or None.")
        return None

    #print(f"  → Processing DataFrame with {len(df)} rows...")

    # Calculate WMAs
    df_processed = calculate_wma(df)
    
    #Calculate RSI and ATR
    df_processed = calculate_rsi_atr(df_processed)

    # Detect crossovers
    df_processed = detect_crossovers(df_processed)

    # Detect supply and demand zones
    df_processed = detect_supply_demand_zones(df_processed)

    # Validate zones and track their status
    df_processed = validate_zones(df_processed)
    df_processed, Supply_Zone_affected = update_supply_zone_status(df_processed)
    df_processed, Demand_Zone_affected = update_demand_zone_status(df_processed)

    # Concatenate affected zones, handling empty DataFrames
    if not Supply_Zone_affected.empty and not Demand_Zone_affected.empty:
        logging.info(f"  → Both supply and demand zones affected by latest candle {df_processed['timestamp'].iloc[-1]}")
        Zones_affected = pd.concat([Supply_Zone_affected, Demand_Zone_affected])
    elif not Supply_Zone_affected.empty:
        logging.info(f"  → Only Supply zones affected by latest candle {df_processed['timestamp'].iloc[-1]}")
        Zones_affected = Supply_Zone_affected
    elif not Demand_Zone_affected.empty:
        logging.info(f"  → Only Demand zones affected by latest candle {df_processed['timestamp'].iloc[-1]}")
        Zones_affected = Demand_Zone_affected
    else:
        logging.info(f"  → No zones affected by latest candle {df_processed['timestamp'].iloc[-1]}")
        Zones_affected = pd.DataFrame()

    # Identify strong zones
    df_processed = identify_strong_zones(df_processed)
    
    #print("  ✓ DataFrame processing complete.")
    return df_processed, Zones_affected

