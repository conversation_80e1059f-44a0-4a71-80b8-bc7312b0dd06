# trading_logic.py

import datetime
import logging
import os
import pandas as pd

import config.config as config
import asyncio
from utils.tele_bot import send_telegram_message
from utils.processing_functions import find_closest_zones_to_current_candle
from utils.processing_functions import identify_zone_on_zone

import warnings
# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')


def find_latest_signals(df):
    """
    Analyze a processed option DataFrame to identify the most recent trading signals.
    """
    result = {'latest_crossover': None, 'latest_zone': None}
    if df is None or df.empty:
        return result

    required_cols = ['timestamp', 'crossover_signal', 'Zones_sup_dem', 'Zone_Status']
    if not all(col in df.columns for col in required_cols):
        return result

    try:
        mask_crossover = df['crossover_signal'].isin(['upward', 'downward'])
        if mask_crossover.any():
            last_idx = df[mask_crossover].index[-1]
            result['latest_crossover'] = df.iloc[last_idx]
    except Exception:
        pass

    try:
        mask_zone = (df['Zones_sup_dem'].isin(['Supply', 'Demand']) &
                     df['Zones_sup_dem'].notna() &
                     (df['Zones_sup_dem'].astype(str).str.strip() != '') &
                     df['Zone_Status'].isin(['Valid', 'Tested']))
        if mask_zone.any():
            last_idx = df[mask_zone].index[-1]
            prior_to_last_idx = df[mask_zone].index[-2]
            result['latest_zone'] = df.iloc[last_idx]
            result['prior_to_latest_zone'] = df.iloc[prior_to_last_idx]
    except Exception:
        pass
    
    return result


def look_for_trade_signals(df_1, df_5, df_60, trade_resistant_df, instrument_name=None, Zones_affected_1=None, Zones_affected_5=None):
    """
    Looks for trade signals based on 1-min and 5-min data.
    """
    entry_reason = "None"
    logging.info("Entering function: look_for_trade_signals")
    if df_1 is None or df_5 is None or df_60 is None or df_1.empty or df_5.empty or df_60.empty:
        logging.info("Exiting look_for_trade_signals: Input DataFrame is empty.")
        return "None", 'False', entry_reason

    latest_1min = find_latest_signals(df_1)
    latest_5min = find_latest_signals(df_5)
    latest_60min = find_latest_signals(df_60)
    
    last_stored_1min_data = df_1.iloc[-1]
    prior_to_last_stored_1min_data = df_1.iloc[-2]
    two_prior_to_last_stored_1min_data = df_1.iloc[-3]
    last_stored_5min_data = df_5.iloc[-1]
    last_stored_60min_data = df_60.iloc[-1]
    Zone_on_5min_zone = False
    Zone_on_1min_zone = False
    
    # df_zone_on_zone = identify_zone_on_zone(df_5, latest_1min['latest_zone'], latest_1min['latest_zone']['Zones_sup_dem'])
    # if not df_zone_on_zone.empty:
    #     Zone_on_5min_zone = True
    #     logging.info(f"Found zone on zone for 5 min: {df_zone_on_zone.iloc[0]}")
    # else:
    #     df_zone_on_zone = identify_zone_on_zone(df_1, latest_1min['latest_zone'], latest_1min['latest_zone']['Zones_sup_dem'])
    #     if not df_zone_on_zone.empty:
    #         Zone_on_1min_zone = True
    #         logging.info(f"Found zone on zone for 1 min: {df_zone_on_zone.iloc[0]}")
    
    # # Print all entries of df_zone_on_zone
    # if df_zone_on_zone is not None and not df_zone_on_zone.empty:
    #     for idx, row in df_zone_on_zone.iterrows():
    #         logging.info(f"Zone on Zone Entry {idx}:")
    #         logging.info(row)
    #         logging.info("-" * 40)
    # else:
    #     logging.info("No zone-on-zone entries found.")

    # logging.info(f"Zone_on_5min_zone: {Zone_on_5min_zone}, Zone_on_1min_zone: {Zone_on_1min_zone}")


    if (latest_1min['latest_crossover'] is not None and
        latest_1min['latest_crossover']['timestamp'] != last_stored_1min_data['timestamp']):
        
        logging.info(f" Difference between latest crossover: {latest_1min['latest_crossover']['timestamp'] - last_stored_1min_data['timestamp']}")
        logging.info(f" Absolute Difference between latest crossover: {abs(latest_1min['latest_crossover']['timestamp'] - last_stored_1min_data['timestamp'])}")
        if (last_stored_1min_data['timestamp'] - latest_1min['latest_crossover']['timestamp']) > datetime.timedelta(minutes=3): 
            logging.info(f"Crossover happened long back. No Trade {latest_1min['latest_crossover']['timestamp']}")
            return "None", 'False', entry_reason
        else:
            if latest_5min['latest_crossover'] is not None:
                df_zone_on_zone = identify_zone_on_zone(df_5, latest_1min['latest_zone'], latest_1min['latest_zone']['Zones_sup_dem'])
                if not df_zone_on_zone.empty:
                    Zone_on_5min_zone = True
                    logging.info(f"Found zone on zone for 5 min: {df_zone_on_zone.iloc[0]}")
                else:
                    df_zone_on_zone = identify_zone_on_zone(df_1, latest_1min['latest_zone'], latest_1min['latest_zone']['Zones_sup_dem'])
                    if not df_zone_on_zone.empty:
                        Zone_on_1min_zone = True
                        logging.info(f"Found zone on zone for 1 min: {df_zone_on_zone.iloc[0]}")

                logging.info(f"Zone_on_5min_zone: {Zone_on_5min_zone}, Zone_on_1min_zone: {Zone_on_1min_zone}")
                #Print all entries of df_zone_on_zone
                if df_zone_on_zone is not None and not df_zone_on_zone.empty:
                    for idx, row in df_zone_on_zone.iterrows():
                        logging.info(f"Zone on Zone Entry {idx}:")
                        logging.info(row)
                        logging.info("-" * 40)
                else:
                    logging.info("No zone-on-zone entries found.")

                logging.info(f"1-min latest crossover: {latest_1min['latest_crossover']['crossover_signal']} timestamp: {latest_1min['latest_crossover']['timestamp']}")
                logging.info(f"5-min latest crossover: {latest_5min['latest_crossover']['crossover_signal']} timestamp: {latest_5min['latest_crossover']['timestamp']}")
                logging.info(f"1-min wma5: {last_stored_1min_data['WMA5']} and wma10: {last_stored_1min_data['WMA10']}")
                logging.info(f"5-min wma5: {last_stored_5min_data['WMA5']} and wma10: {last_stored_5min_data['WMA10']}")
                logging.info(f"1-min high: {last_stored_1min_data['high']} and low: {last_stored_1min_data['low']}")
                logging.info(f"5-min high: {last_stored_5min_data['high']} and low: {last_stored_5min_data['low']}")
                logging.info(f"1-min RSI: {last_stored_1min_data['RSI_14']} and ATR: {last_stored_1min_data['ATR_14']}")
                logging.info(f"5-min RSI: {last_stored_5min_data['RSI_14']} and ATR: {last_stored_5min_data['ATR_14']}")
                logging.info(f"60-min latest crossover: {latest_60min['latest_crossover']['crossover_signal']} timestamp: {latest_60min['latest_crossover']['timestamp']}")
                logging.info(f"60-min RSI: {last_stored_60min_data['RSI_14']} and ATR: {last_stored_60min_data['ATR_14']}")
                
            
                is_put_signal = ((latest_5min['latest_crossover']['crossover_signal'] == 'downward' and
                             latest_1min['latest_crossover']['crossover_signal'] == 'downward' and
                             last_stored_1min_data["WMA5"] < last_stored_1min_data["WMA10"] and 
                             last_stored_5min_data["WMA5"] < last_stored_5min_data["WMA10"] and
                             last_stored_1min_data["high"] < last_stored_5min_data["WMA45"] and
                             latest_1min['latest_zone']['low'] < last_stored_5min_data["WMA45"] and #Exclusive
                            (last_stored_1min_data["WMA5"] < last_stored_1min_data["WMA90"] or  #Exclusive
                             last_stored_1min_data["WMA10"] < last_stored_1min_data["WMA65"])) or  #Exclusive

                            (latest_5min['latest_crossover']['crossover_signal'] == 'downward' and
                             latest_1min['latest_crossover']['crossover_signal'] == 'downward' and
                             (Zone_on_5min_zone or Zone_on_1min_zone) and 
                             last_stored_1min_data['low'] < prior_to_last_stored_1min_data['low'] and #Exclusive
                             last_stored_1min_data['high'] < prior_to_last_stored_1min_data['high'] and #Exclusive
                             last_stored_1min_data["high"] < last_stored_5min_data["WMA45"]) or

                            (latest_5min['latest_crossover']['crossover_signal'] == 'downward' and #Exclusive
                             latest_1min['latest_crossover']['crossover_signal'] == 'downward' and #Exclusive
                             latest_1min['latest_zone']['high'] < latest_1min['latest_zone']["WMA90"] and #Exclusive
                             last_stored_1min_data['low'] < prior_to_last_stored_1min_data['low'] and #Exclusive
                             last_stored_1min_data['high'] < prior_to_last_stored_1min_data['high'] and #Exclusive
                             last_stored_1min_data["high"] < last_stored_5min_data["WMA45"])) #Exclusive
                
                is_call_signal = ((latest_5min['latest_crossover']['crossover_signal'] == 'upward' and
                              latest_1min['latest_crossover']['crossover_signal'] == 'upward' and
                              last_stored_1min_data["WMA5"] > last_stored_1min_data["WMA10"] and 
                              last_stored_5min_data["WMA5"] > last_stored_5min_data["WMA10"] and
                              last_stored_1min_data["low"] > last_stored_5min_data["WMA45"] and
                            (last_stored_1min_data["WMA5"] > last_stored_1min_data["WMA90"] or #Exclusive
                              last_stored_1min_data["WMA10"] > last_stored_1min_data["WMA65"])) or #Exclusive

                            (latest_5min['latest_crossover']['crossover_signal'] == 'upward' and
                             latest_1min['latest_crossover']['crossover_signal'] == 'upward' and
                             (Zone_on_5min_zone or Zone_on_1min_zone) and 
                             last_stored_1min_data['high'] > prior_to_last_stored_1min_data['high'] and #Exclusive
                             last_stored_1min_data['low'] > prior_to_last_stored_1min_data['low'] and #Exclusive
                             last_stored_1min_data["low"] > last_stored_5min_data["WMA45"]) or

                            (latest_5min['latest_crossover']['crossover_signal'] == 'upward' and #Exclusive
                              latest_1min['latest_crossover']['crossover_signal'] == 'upward' and #Exclusive
                              latest_1min['latest_zone']['low'] > latest_1min['latest_zone']["WMA90"] and #Exclusive
                              last_stored_1min_data['high'] > prior_to_last_stored_1min_data['high'] and #Exclusive
                              last_stored_1min_data['low'] > prior_to_last_stored_1min_data['low'] and #Exclusive
                              last_stored_1min_data["low"] > last_stored_5min_data["WMA45"])) #Exclusive

            if is_put_signal:
                if (latest_5min['latest_crossover']['crossover_signal'] == 'downward' and
                    latest_1min['latest_crossover']['crossover_signal'] == 'downward' and
                    last_stored_1min_data["WMA5"] < last_stored_1min_data["WMA10"] and 
                    last_stored_5min_data["WMA5"] < last_stored_5min_data["WMA10"] and
                    last_stored_1min_data["high"] < last_stored_5min_data["WMA45"] and
                    (last_stored_1min_data["WMA5"] < last_stored_1min_data["WMA90"] or  #Exclusive
                    last_stored_1min_data["WMA10"] < last_stored_1min_data["WMA65"])):
                    logging.info("Puts because both 5 min and 1 min have downward crossovers")
                    entry_reason = "Regular_Entry"
                
                if (latest_5min['latest_crossover']['crossover_signal'] == 'downward' and #Exclusive
                    latest_1min['latest_crossover']['crossover_signal'] == 'downward' and #Exclusive
                    latest_1min['latest_zone']['high'] < latest_1min['latest_zone']["WMA90"] and #Exclusive
                    last_stored_1min_data['low'] < prior_to_last_stored_1min_data['low'] and #Exclusive
                    last_stored_1min_data['high'] < prior_to_last_stored_1min_data['high'] and #Exclusive
                    last_stored_1min_data["high"] < last_stored_5min_data["WMA45"]): #Exclusive
                    logging.info("Puts because crossover is below Grand Father") #Exclusive
                    entry_reason = "Regular_Entry"  #Exclusive

                if (latest_5min['latest_crossover']['crossover_signal'] == 'downward' and
                    latest_1min['latest_crossover']['crossover_signal'] == 'downward' and
                    (Zone_on_5min_zone or Zone_on_1min_zone) and 
                    last_stored_1min_data['low'] < prior_to_last_stored_1min_data['low'] and #Exclusive
                    last_stored_1min_data['high'] < prior_to_last_stored_1min_data['high'] and #Exclusive
                    last_stored_1min_data["high"] < last_stored_5min_data["WMA45"]):
                    logging.info("Puts because of Zone on Zone ")
                    entry_reason = "Zone_on_Zone"

                logging.info(f"last stored low: {last_stored_1min_data['low']} and prior to last stored low: {prior_to_last_stored_1min_data['low']}")
                logging.info(f"last stored high: {last_stored_1min_data['high']} and prior to last stored high: {prior_to_last_stored_1min_data['high']}")

                # if (
                #     trade_resistant_df is not None and
                #     not trade_resistant_df.empty and
                #     ((trade_resistant_df['Resistant_Zone'] == 'Demand') &
                #     (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
                #     ):
                #         num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
                #         num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
                #         if num_demand > num_supply:
                #             logging.info(f"Just hit bigger demand so no Puts: Supplies {num_supply}, Demands: {num_demand}")
                #             return "None", 'False', entry_reason
                #         else:
                #             logging.info("PUT signal identified. - 2 ")
                #             # Message to be sent for telegram bot   
                #             message_to_send = f"===Algo_Trading===$INTIMATION$Get ready for {instrument_name} PUT option trade."
                #             logging.debug(message_to_send)
                #             #asyncio.run(send_telegram_message(message_to_send))
                #             # Alert to Mobile " Take PUTS for Nifty 50"
                #             return "PUT", 'True', entry_reason
                # if ((Zones_affected_1 is not None and not Zones_affected_1.empty and
                #       (Zones_affected_1['Zones_sup_dem'] == 'Demand').any()) or
                #       (Zones_affected_5 is not None and not Zones_affected_5.empty and
                #       (Zones_affected_5['Zones_sup_dem'] == 'Demand').any())
                #       ):
                #     logging.info("Just hit demand Zone, so no Puts")
                #     return "None", 'False', entry_reason
                # else:
                logging.info("PUT signal identified. - 1 ")
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$INTIMATION$Get ready for {instrument_name} PUT option trade."
                logging.debug(message_to_send)
                #asyncio.run(send_telegram_message(message_to_send))
                # Alert to Mobile " Take PUTS for Nifty 50"
                return "PUT", 'True', entry_reason
            
            if is_call_signal:
                if (latest_5min['latest_crossover']['crossover_signal'] == 'upward' and
                    latest_1min['latest_crossover']['crossover_signal'] == 'upward' and
                    last_stored_1min_data["WMA5"] > last_stored_1min_data["WMA10"] and 
                    last_stored_5min_data["WMA5"] > last_stored_5min_data["WMA10"] and
                    last_stored_1min_data["low"] > last_stored_5min_data["WMA45"] and
                    (last_stored_1min_data["WMA5"] > last_stored_1min_data["WMA90"] or #Exclusive
                    last_stored_1min_data["WMA10"] > last_stored_1min_data["WMA65"])): #Exclusive
                    logging.info("Calls because both 5 min and 1 min have upward crossovers")
                    entry_reason = "Regular_Entry"

                if (latest_5min['latest_crossover']['crossover_signal'] == 'upward' and #Exclusive
                    latest_1min['latest_crossover']['crossover_signal'] == 'upward' and #Exclusive
                    latest_1min['latest_zone']['low'] > latest_1min['latest_zone']["WMA90"] and #Exclusive
                    last_stored_1min_data['high'] > prior_to_last_stored_1min_data['high'] and #Exclusive
                    last_stored_1min_data['low'] > prior_to_last_stored_1min_data['low'] and #Exclusive
                    last_stored_1min_data["low"] > last_stored_5min_data["WMA45"]): #Exclusive
                    logging.info("Calls because crossover is Above Grand Father") #Exclusive
                    entry_reason = "Regular_Entry" #Exclusive

                if (latest_5min['latest_crossover']['crossover_signal'] == 'upward' and
                    latest_1min['latest_crossover']['crossover_signal'] == 'upward' and
                    (Zone_on_5min_zone or Zone_on_1min_zone) and 
                    last_stored_1min_data['high'] > prior_to_last_stored_1min_data['high'] and #Exclusive
                    last_stored_1min_data['low'] > prior_to_last_stored_1min_data['low'] and #Exclusive
                    last_stored_1min_data["low"] > last_stored_5min_data["WMA45"]):
                    logging.info("Calls because of Zone on Zone ")
                    entry_reason = "Zone_on_Zone"

                logging.info(f"last stored low: {last_stored_1min_data['low']} and prior to last stored low: {prior_to_last_stored_1min_data['low']}")
                logging.info(f"last stored high: {last_stored_1min_data['high']} and prior to last stored high: {prior_to_last_stored_1min_data['high']}")

                # if (
                #     trade_resistant_df is not None and
                #     not trade_resistant_df.empty and
                #     ((trade_resistant_df['Resistant_Zone'] == 'Supply') &
                #     (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
                #     ):
                #         num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
                #         num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
                #         if num_supply > num_demand:
                #             logging.info(f"Just hit bigger supply so no Calls: Supplies {num_supply}, Demands: {num_demand}")
                #             return "None", 'False', entry_reason
                #         else:
                #             logging.info("CALL signal identified. - 2 ")
                #             # Message to be sent for telegram bot   
                #             message_to_send = f"===Algo_Trading===$INTIMATION$Get ready for {instrument_name} CALL option trade."
                #             logging.debug(message_to_send)
                #             #asyncio.run(send_telegram_message(message_to_send))
                #             # Alert to Mobile "Take calls for Nifty 50"
                #             return "CALL", 'True', entry_reason
                # if ((Zones_affected_1 is not None and not Zones_affected_1.empty and
                #       (Zones_affected_1['Zones_sup_dem'] == 'Supply').any()) or
                #       (Zones_affected_5 is not None and not Zones_affected_5.empty and
                #       (Zones_affected_5['Zones_sup_dem'] == 'Supply').any())
                #       ):
                #     logging.info("Just hit supply Zone, so no Calls")
                #     return "None", 'False', entry_reason
                # else:
                logging.info("CALL signal identified. -1 ")
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$INTIMATION$Get ready for {instrument_name} CALL option trade."
                logging.debug(message_to_send)
                #asyncio.run(send_telegram_message(message_to_send))
                # Alert to Mobile "Take calls for Nifty 50"
                return "CALL", 'True', entry_reason

    if ((prior_to_last_stored_1min_data['strong_zone'] == 'Reason_for_strong_upward' or
        two_prior_to_last_stored_1min_data['strong_zone'] == 'Reason_for_strong_upward') and
        last_stored_1min_data['low'] >= last_stored_5min_data['WMA45']):
        logging.info("CALL signal identified. -3 ")
        entry_reason = "Strong_Zone_Entry"
        # Message to be sent for telegram bot   
        message_to_send = f"===Algo_Trading===$INTIMATION$Get ready for {instrument_name} CALL option trade."
        logging.debug(message_to_send)
        #asyncio.run(send_telegram_message(message_to_send))
        return "CALL", 'True', entry_reason
    elif ((prior_to_last_stored_1min_data['strong_zone'] == 'Reason_for_strong_downward' or
        two_prior_to_last_stored_1min_data['strong_zone'] == 'Reason_for_strong_downward') and
        last_stored_1min_data['high'] <= last_stored_5min_data['WMA45']):
        logging.info("PUT signal identified. -3 ")
        entry_reason = "Strong_Zone_Entry"
        # Message to be sent for telegram bot   
        message_to_send = f"===Algo_Trading===$INTIMATION$Get ready for {instrument_name} PUT option trade."
        logging.debug(message_to_send)
        #asyncio.run(send_telegram_message(message_to_send))
        return "PUT", 'True', entry_reason

    logging.info(f"Exiting function: look_for_trade_signals (no signal found for {instrument_name})")
    return "None", 'False', entry_reason


def get_info_for_trade(df_1, master_dataframes, option_type_to_trade, signal_time, trade_resistant_df):
    """
    Finds the trading symbol and latest candle for the specified option type.
    """
    logging.info(f"Entering function: Get_info_for_trade for {option_type_to_trade}")
    for symbol in master_dataframes:
        if symbol.split()[-1] == option_type_to_trade:
            logging.info(f"Found matching symbol: {symbol}")
            latest_1min_option_candle = master_dataframes[symbol]['df_opt_1min'].iloc[-1]
            # Exclusive
            last_stored_1min_data = df_1.iloc[-1]
            if option_type_to_trade == "PUT":
                if last_stored_1min_data['high'] > last_stored_1min_data["WMA10"]:
                    logging.info(f"PUTS not valid anymore. High is above WMA10. No Trade")
                    return None, None
            else:
                if last_stored_1min_data['low'] < last_stored_1min_data["WMA10"]:
                    logging.info(f"Calls not valid anymore. Low is below WMA10. No Trade")
                    return None, None

            # if option_type_to_trade == "PUT":
            #     if (
            #         trade_resistant_df is not None and
            #         not trade_resistant_df.empty and
            #         ((trade_resistant_df['Resistant_Zone'] == 'Demand') &
            #         (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
            #         ):
            #             num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
            #             num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
            #             if num_demand > num_supply:
            #                 logging.info("Inside get_info_for_trade: Just hit bigger demand so no Puts")
            #                 return None, None
            # else:
            #     if (
            #         trade_resistant_df is not None and
            #         not trade_resistant_df.empty and
            #         ((trade_resistant_df['Resistant_Zone'] == 'Supply') &
            #         (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
            #         ):
            #             num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
            #             num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
            #             if num_supply > num_demand:
            #                 logging.info("Inside get_info_for_trade: Just hit bigger supply so no Calls")
            #                 return None, None

            Option_1min_zone_candle = find_latest_signals(master_dataframes[symbol]['df_opt_1min'])
            if Option_1min_zone_candle['latest_crossover'] is not None:
                time_diff = Option_1min_zone_candle['latest_crossover']['timestamp'] - signal_time
                time_diff_min = abs(time_diff.total_seconds()) / 60
                logging.info(f"Trading Signal came at  time: {signal_time}")
                logging.info(f"Option 1 min crossover: {Option_1min_zone_candle['latest_crossover']['crossover_signal']} at time : {Option_1min_zone_candle['latest_crossover']['timestamp']}")
                logging.info(f"difference of time between last option crossover and signal time: {time_diff} and in minutes: {time_diff_min}")
                logging.info(f"Latest 1 min option candle: {latest_1min_option_candle['timestamp']} High: {latest_1min_option_candle['high']}, Low: {latest_1min_option_candle['low']}")
                if (Option_1min_zone_candle['latest_crossover']['crossover_signal'] == 'upward' and 
                    time_diff_min <= 2):
                    logging.info(f"Found latest Option crossover for {symbol}: {Option_1min_zone_candle['latest_crossover']['timestamp']}")
                    return symbol, latest_1min_option_candle
                elif (Option_1min_zone_candle['latest_crossover']['crossover_signal'] == 'downward' and 
                    time_diff_min > 2):
                    logging.info(f"Did not find latest Option crossover for {symbol}: {Option_1min_zone_candle['latest_crossover']['timestamp']} signal time {signal_time}")
                    return 'Not_yet', latest_1min_option_candle
                else:
                    logging.info(f"Signal time is too old for {symbol}. Ignoring.")
                    return None, None
            else:
                return None, None
            # Exclusive
    logging.info("No matching symbol found.")
    return None, None


def take_the_trade(symbol, option_1min_ready_for_trade_candle, master_dataframes, trade_resistant_df):
    """
    Determines if a trade should be taken based on the latest option data.
    """
    logging.info(f"Entering function: take_the_trade for {symbol}")
    latest_1min_option_candle = master_dataframes[symbol]['df_opt_1min'].iloc[-1]
    option_type = symbol.split()[-1]

    if option_type == "PUT":
        if (latest_1min_option_candle['crossover_signal'] == 'downward' and
            latest_1min_option_candle['timestamp'] > option_1min_ready_for_trade_candle['timestamp']):
            logging.info(f"PUT Trade invalidated. Signal reversed.")
            return latest_1min_option_candle['timestamp'], "False"
        # Exclusive
        # if (
        #     trade_resistant_df is not None and
        #     not trade_resistant_df.empty and
        #     ((trade_resistant_df['Resistant_Zone'] == 'Demand') &
        #     (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
        #     ):
        #         num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
        #         num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
        #         if num_demand > num_supply:
        #             logging.info("Inside take_the_trade: Just hit bigger demand so no Puts")
        #             return latest_1min_option_candle['timestamp'], "False"
        # Exclusive
        if latest_1min_option_candle['low'] <= latest_1min_option_candle['WMA5']:
            logging.info(f"PUT Trade Taken at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['low']}")
            return latest_1min_option_candle['timestamp'], "Trade Taken"
        

    elif option_type == "CALL":
        if (latest_1min_option_candle['crossover_signal'] == 'downward' and
            latest_1min_option_candle['timestamp'] > option_1min_ready_for_trade_candle['timestamp']):
            logging.info(f"CALL Trade invalidated. Signal reversed.")
            return latest_1min_option_candle['timestamp'], "False"
        # Exclusive
        # if (
        #     trade_resistant_df is not None and
        #     not trade_resistant_df.empty and
        #     ((trade_resistant_df['Resistant_Zone'] == 'Supply') &
        #     (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
        #     ):
        #         num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
        #         num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
        #         if num_supply > num_demand:
        #             logging.info("Inside take_the_trade: Just hit bigger supply so no Calls")
        #             return latest_1min_option_candle['timestamp'], "False"
        # Exclusive        
        if latest_1min_option_candle['low'] <= latest_1min_option_candle['WMA5']:
            logging.info(f"CALL Trade Taken at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['low']}")
            return latest_1min_option_candle['timestamp'], "Trade Taken"
        
    else:
        logging.info(f"No trade taken. Invalid symbol type {symbol}")
        return latest_1min_option_candle['timestamp'], "False"
    
    logging.info("Exiting function: take_the_trade (conditions not met)")
    return None, "Take trade"


def check_for_trade_exit(df_1, df_5, trade_symbol, time_trade_taken, 
                         master_dataframes, trade_log_df, trade_resistant_df, opt_1min_latest_Demand, 
                         opt_1min_latest_Supply, opt_5min_latest_Demand, opt_5min_latest_Supply, stop_loss, entry_reason, Zones_affected_1, Zones_affected_5):
    """
    Checks for conditions to exit an ongoing trade.
    """
    logging.info(f"Entering function: check_for_trade_exit for {trade_symbol}")
    latest_1min_option_candle = master_dataframes[trade_symbol]['df_opt_1min'].iloc[-1]
    latest_5min_option_candle = master_dataframes[trade_symbol]['df_opt_5min'].iloc[-1]
    Option_1min_crossover_and_zone = find_latest_signals(master_dataframes[trade_symbol]['df_opt_1min'])
    result_1min = find_latest_signals(df_1)
    result_5min = find_latest_signals(df_5)
    option_type = trade_symbol.split()[-1]
    entry_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'Trade_entry_price'].iloc[0]
    current_opt_price = latest_1min_option_candle['close']

    if (latest_5min_option_candle['WMA10'] > stop_loss + 10 or  #To be in profit
        stop_loss > opt_1min_latest_Demand['low']): #To keep trailing stop loss
        if stop_loss < latest_5min_option_candle['WMA10']:
            logging.info(f"Latest option 1 min demand at: {opt_1min_latest_Demand['timestamp']} and low value: {opt_1min_latest_Demand['low']}")
            logging.info(f"Adjusting stop loss 1: from {stop_loss} to {latest_5min_option_candle['WMA10']}")
            stop_loss = latest_5min_option_candle['WMA10']
            # Message to be sent for telegram bot   
            message_to_send = message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol}"
            logging.debug(message_to_send)
            #asyncio.run(send_telegram_message(message_to_send))

    if (option_type == "CALL" and result_5min['prior_to_latest_zone']['Zones_sup_dem'] == 'Supply'):
        if (df_1.iloc[-1]['high'] >= result_5min['prior_to_latest_zone']['low']):
            if stop_loss < latest_5min_option_candle['WMA10']:
                logging.info(f"Adjusting stop loss 2: from {stop_loss} to {latest_5min_option_candle['WMA10']}")
                logging.info(f"Latest index 1 min candle high: {df_1.iloc[-1]['high']} and previous {result_5min['prior_to_latest_zone']['crossover_signal']} zone low value is: {result_5min['prior_to_latest_zone']['low']}")
                stop_loss = latest_5min_option_candle['WMA10']
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol}"
                logging.debug(message_to_send)
                #asyncio.run(send_telegram_message(message_to_send))
    elif (option_type == "PUT" and result_5min['prior_to_latest_zone']['Zones_sup_dem'] == 'Demand'):
          if (df_1.iloc[-1]['low'] <= result_5min['prior_to_latest_zone']['high']):
            if stop_loss < latest_5min_option_candle['WMA10']:
                logging.info(f"Latest index 1 min candle high: {df_1.iloc[-1]['low']} and previous {result_5min['prior_to_latest_zone']['crossover_signal']} zone high value is: {result_5min['prior_to_latest_zone']['high']}")
                logging.info(f"Adjusting stop loss 3: from {stop_loss} to {latest_5min_option_candle['WMA10']}")
                stop_loss = latest_5min_option_candle['WMA10']
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$Update$STOP LOSS to${stop_loss}$for option${trade_symbol}"
                logging.debug(message_to_send)
                #asyncio.run(send_telegram_message(message_to_send))
            
    # When price hits opposite resistant zone, get ready for exit by adjusting stoploss
    if not trade_resistant_df.empty:
        if (option_type == "PUT" and 
            trade_resistant_df is not None and
            not trade_resistant_df.empty and
            ((trade_resistant_df['Resistant_Zone'] == 'Demand') &
            (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
            ):
            num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
            num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
            if num_demand > num_supply:
                if stop_loss < latest_5min_option_candle['WMA5']:
                    stop_loss = latest_5min_option_candle['WMA5']
                    nearest_demand = trade_resistant_df[trade_resistant_df['Resistant_Zone'] == 'Demand'].iloc[0]
                    logging.info(f"Found 5 minute demand zone at {nearest_demand['low']} - {nearest_demand['high']}")
                    #logging.info(f"and zone timestamp: {nearest_demand['timestamp']}")
        elif (option_type == "CALL" and 
            trade_resistant_df is not None and
            not trade_resistant_df.empty and
            ((trade_resistant_df['Resistant_Zone'] == 'Supply') &
            (trade_resistant_df['Zone_Status'].isin(['Valid', 'Tested']))).any()
            ):
            num_demand = (trade_resistant_df['Resistant_Zone'] == 'Demand').sum()
            num_supply = (trade_resistant_df['Resistant_Zone'] == 'Supply').sum()
            if num_supply > num_demand:
                if stop_loss < latest_5min_option_candle['WMA5']:
                    stop_loss = latest_5min_option_candle['WMA5']
                    nearest_supply = trade_resistant_df[trade_resistant_df['Resistant_Zone'] == 'Supply'].iloc[0]
                    logging.info(f"Found 5 minute supply zone at {nearest_supply['low']} - {nearest_supply['high']}")
                    #logging.info(f"and zone timestamp: {nearest_supply['timestamp']}")

    logging.info(f"Stop loss: {stop_loss}")

    # Profit target exit
    # if (latest_1min_option_candle['low'] > (trade_log_recent_entry['Trade_entry_price'] + 15)):
    #     logging.info(f"Exit for profit target: {trade_symbol}")
    #     # Alert to Mobile " Move stop loss to profit trade_symbol (extract from symbol puts or calls) for Nifty 50"
    #     return 'Trade Exit', stop_loss
    
    # Stop loss exit
    #if (latest_1min_option_candle['low'] <= (latest_5min_option_candle['WMA10'] - 0.10)):
    if (latest_1min_option_candle['low'] < stop_loss):
        logging.info(f"Exit for stop loss: {trade_symbol} and stop loss trigger is {stop_loss} at current price: {current_opt_price} (Entry price: {entry_price})")
        # Alert to Mobile " Exit trade_symbol (extract from symbol puts or calls) for Nifty 50"
        # Message to be sent for telegram bot   
        message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['low']}${latest_1min_option_candle['timestamp']}"
        logging.debug(message_to_send)
        #asyncio.run(send_telegram_message(message_to_send))
        return 'Trade Exit', stop_loss
    
    # Time-based exit
    # if ((latest_1min_option_candle['timestamp'] - time_trade_taken) > datetime.timedelta(minutes=15) and 
    #    (latest_1min_option_candle['low'] <= (trade_log_recent_entry['Trade_entry_price']))):
    #     logging.info(f"Exit due to time limit: {trade_symbol}")
    #     # Alert to Mobile " Exit trade_symbol (extract from symbol puts or calls) for Nifty 50"
    #     return 'Trade Exit', stop_loss
    

    # Signal reversal exit
    if option_type == "PUT":
        if (result_1min['latest_crossover'] is not None and
            result_1min['latest_crossover']['crossover_signal'] == 'upward' and
            result_1min['latest_crossover']['timestamp'] > time_trade_taken):
            if entry_reason == "Zone_on_Zone":
                logging.info(f"Exit checking for Zone on Zone scenario")
                if ((df_1.iloc[-1]['high'] > df_5.iloc[-1]['WMA5']) or
                    (df_1.iloc[-1]['timestamp'] - result_1min['latest_crossover']['timestamp']) > datetime.timedelta(minutes=2)):
                    logging.info(f"Exit PUT trade for Zone on Zone scenario at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")
                    message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['low']}${latest_1min_option_candle['timestamp']}"
                    logging.debug(message_to_send)
                    #asyncio.run(send_telegram_message(message_to_send))
                    return 'Trade Exit', stop_loss
            else:
                if (Option_1min_crossover_and_zone['latest_zone']['crossover_signal'] == 'downward' and
                    Option_1min_crossover_and_zone['latest_zone']['timestamp'] > time_trade_taken):
                    logging.info(f"Exit PUT trade due to upward crossover at index and downward crossover at option. at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")    
                else:
                    logging.info(f"Exit PUT trade due to upward crossover. at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")
                # Alert to Mobile " Exit trade_symbol (extract from symbol puts or calls) for Nifty 50"
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['low']}${latest_1min_option_candle['timestamp']}"
                logging.debug(message_to_send)
                #asyncio.run(send_telegram_message(message_to_send))
                return 'Trade Exit', stop_loss
        # if Zones_affected_5 is not None and not Zones_affected_5.empty:
        #     logging.info(f"5 minute Zones affected during PUTS: {Zones_affected_5}")
        #     if (Zones_affected_5['Zones_sup_dem'] == 'Demand').any():
        #         logging.info(f"Found demand zone in 5 min")
        #         if stop_loss < latest_1min_option_candle['low']:
        #             stop_loss = latest_1min_option_candle['low']
        # elif Zones_affected_1 is not None and not Zones_affected_1.empty:
        #     logging.info(f"1 minute Zones affected during PUTS: {Zones_affected_1}")
        #     if (Zones_affected_1['Zones_sup_dem'] == 'Demand').any():
        #         logging.info(f"Found demand zone in 1 min")
        #         if stop_loss < latest_1min_option_candle['low']:
        #             stop_loss = latest_1min_option_candle['low']
    elif option_type == "CALL":
        if (result_1min['latest_crossover'] is not None and
            result_1min['latest_crossover']['crossover_signal'] == 'downward' and
            result_1min['latest_crossover']['timestamp'] > time_trade_taken):
            if entry_reason == "Zone_on_Zone":
                logging.info(f"Exit Checking for Zone on Zone scenario")
                if ((df_1.iloc[-1]['low'] < df_5.iloc[-1]['WMA5']) and
                    (df_1.iloc[-1]['timestamp'] - result_1min['latest_crossover']['timestamp']) >= datetime.timedelta(minutes=2)):
                    logging.info(f"Exit CALL trade for Zone on Zone scenario at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")
                    message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['low']}${latest_1min_option_candle['timestamp']}"
                    logging.debug(message_to_send)
                    #asyncio.run(send_telegram_message(message_to_send))
                    return 'Trade Exit', stop_loss
            else:
                if (Option_1min_crossover_and_zone['latest_zone']['crossover_signal'] == 'downward' and
                    Option_1min_crossover_and_zone['latest_zone']['timestamp'] > time_trade_taken):
                    logging.info(f"Exit CALL trade due to downward crossover at index and downward crossover at option. at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")    
                else:
                    logging.info(f"Exit CALL trade due to downward crossover. at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})")
                # Alert to Mobile " Exit trade_symbol (extract from symbol puts or calls) for Nifty 50"
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$TRADE$CLOSE${trade_symbol}${latest_1min_option_candle['low']}${latest_1min_option_candle['timestamp']}"
                logging.debug(message_to_send)
                #asyncio.run(send_telegram_message(message_to_send))
                return 'Trade Exit', stop_loss
        # if Zones_affected_5 is not None and not Zones_affected_5.empty:
        #     logging.info(f"5 minute Zones affected during CALLS: {Zones_affected_5}")
        #     if (Zones_affected_5['Zones_sup_dem'] == 'Supply').any():
        #         logging.info(f"Found supply zone in 5 min")
        #         if stop_loss < latest_1min_option_candle['low']:
        #             stop_loss = latest_1min_option_candle['low']
        # elif Zones_affected_1 is not None and not Zones_affected_1.empty:
        #     logging.info(f"1 minute Zones affected during CALLS: {Zones_affected_1}")
        #     if (Zones_affected_1['Zones_sup_dem'] == 'Supply').any():
        #         logging.info(f"Found supply zone in 1 min")
        #         if stop_loss < latest_1min_option_candle['low']:
        #             stop_loss = latest_1min_option_candle['low']

    logging.info(f"Continue to hold trade: {trade_symbol} at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss}")
    # Message to be sent for telegram bot   
    entry_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == time_trade_taken, 'Trade_entry_price'].iloc[0]
    current_opt_price = latest_1min_option_candle['close']
    message_to_send = f"===Algo_Trading===$INTIMATION$Continue to hold trade for ${trade_symbol} at current price: {current_opt_price} (Entry price: {entry_price}) (Stop loss: {stop_loss})"
    logging.debug(message_to_send)
    #asyncio.run(send_telegram_message(message_to_send))
    return 'Trade Taken', stop_loss


def exit_the_trade(trade_info, master_dataframes, trade_log_df, to_date_str):
    """
    Records the exit details of a trade and saves the log.
    """
    logging.info(f"Entering function: exit_the_trade for {trade_info['symbol']}")
    latest_1min_option_candle = master_dataframes[trade_info['symbol']]['df_opt_1min'].iloc[-1]
    logging.info(f"{trade_info['symbol']} Trade Exited at: {latest_1min_option_candle['timestamp']} price: {latest_1min_option_candle['close']}")
    
    trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'trade_exit_time'] = latest_1min_option_candle['timestamp']
    trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'Trade_exit_price'] = latest_1min_option_candle['close']

    try:
        # user_df = pd.read_csv(config.OPTIONS_CSV_FILE)
        # options_lot_size_df = pd.read_csv(config.OPTIONS_LOT_SIZE_CSV)
        
        # if trade_info['symbol'].split()[0] == "NIFTY":
        #     lot_size = 75
        # elif trade_info['symbol'].split()[0] == "BANKNIFTY":
        #     lot_size = 30
        # else:
        #     lot_size = 65
        lot_size = trade_info['lot_size'].astype(int)

        logging.info(f"Lot size for {trade_info['symbol']} is {lot_size}")

        # Default lot size for NIFTY if not found
        # underlying_symbol = user_df[user_df['DISPLAY_NAME'] == symbol]['UNDERLYING_SYMBOL'].iloc[0]
        # if not options_lot_size_df[options_lot_size_df['SYMBOL'] == underlying_symbol].empty:
        #     lot_size = options_lot_size_df[options_lot_size_df['SYMBOL'] == underlying_symbol]['LOT_SIZE'].iloc[0]

        entry_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'Trade_entry_price'].iloc[0]
        exit_price = trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'Trade_exit_price'].iloc[0]
        
        pnl = (exit_price - entry_price) * lot_size - 60  # Assuming 60 for charges
        trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'profit_loss'] = pnl.round(2)
        logging.info(f"P&L for {trade_info['symbol']}: {pnl}")

    except (FileNotFoundError, IndexError) as e:
        logging.error(f"Could not calculate P&L for {trade_info['symbol']}: {e}")
        trade_log_df.loc[trade_log_df['trade_entry_time'] == trade_info['entry_time'], 'profit_loss'] = 'Calculation Error'

    logging.info(f"Trade log updated: \n{trade_log_df}")

    # Save trade log
    os.makedirs(config.OUTPUT_DIR_TRADE_LOG, exist_ok=True)
    output_filename = f"trade_log_{to_date_str}.csv"
    output_path = os.path.join(config.OUTPUT_DIR_TRADE_LOG, output_filename)
    
    trade_log_to_save = trade_log_df[trade_log_df['trade_entry_time'] == trade_info['entry_time']]

    if os.path.exists(output_path):
        try:
            existing_df = pd.read_csv(output_path)
            combined_df = pd.concat([existing_df, trade_log_to_save], ignore_index=True)
            combined_df.drop_duplicates(subset=["Instrument_name", "trade_entry_time"], keep='last', inplace=True)
            combined_df.to_csv(output_path, index=False)
        except Exception as e:
            logging.error(f"Error appending to trade log: {e}")
    else:
        trade_log_to_save.to_csv(output_path, index=False)

    return True