# /home/<USER>/Algo-trade/Option_trade/main.py (formerly historical_fetcher.py)

import os
import datetime
import pandas as pd
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import config.config as config
import utils.data_handler as data_handler
import utils.trading_logic as trading_logic
from utils.processing_functions import process_dataframe
import threading
from utils.trading_logic import find_latest_signals
import asyncio
from utils.tele_bot import send_telegram_message
from utils.processing_functions import get_resistant_zones
from utils.processing_functions import find_closest_zones_to_current_candle


# === Logging Configuration ===
os.makedirs(config.LOG_DIR, exist_ok=True)
log_filename = os.path.join(config.LOG_DIR, f"Daily_log_{datetime.datetime.now().strftime('%Y-%m-%d')}.log")
logging.basicConfig(
    filename=log_filename,
    level=logging.INFO,
    format='%(asctime)s %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def fetch_and_save_instrument_data(instrument, intervals_to_fetch):
    results = []
    thread_name = threading.current_thread().name
    print(f"[{thread_name}] Processing {instrument['tradingsymbol']}")
    logging.info(f"[{thread_name}] Processing {instrument['tradingsymbol']}")
    for interval in intervals_to_fetch:
        print(f"  → Fetching {instrument['security_id']} {interval}min data...")
        logging.info(f" Fetching {instrument['security_id']} {interval}min data...")
        df, from_date_str, to_date_str = data_handler.fetch_historical_data(
            security_id=instrument["security_id"],
            exchange_segment=instrument["exchange_segment"],
            instrument_type=instrument["instrument_type"],
            interval=interval
        )

        if df is not None and not df.empty:
            print(f"    ✓ Fetched {len(df)} new records")
            logging.info(f" Fetched {len(df)} new records")
            df_processed = process_dataframe(df)

            try:
                os.makedirs(config.OUTPUT_DIR, exist_ok=True)
                instrument_dir = os.path.join(config.OUTPUT_DIR, instrument['tradingsymbol'])
                os.makedirs(instrument_dir, exist_ok=True)
                #today_str = datetime.datetime.now().strftime('%Y-%m-%d')
                day_dir = os.path.join(instrument_dir, to_date_str)
                os.makedirs(day_dir, exist_ok=True)
                interval_suffix = "daily" if interval == "daily" else f"{interval}min"
                output_filename = f"{instrument['tradingsymbol']}_{interval_suffix}_{from_date_str}_to_{to_date_str}_processed.csv"
                output_path = os.path.join(day_dir, output_filename)
                if os.path.exists(output_path):
                    df_processed.to_csv(output_path, mode='a', header=False, index=False)
                else:
                    df_processed.to_csv(output_path, index=False)
                print(f"    ✓ Saved processed file: {output_filename}")
                logging.info(f" Saved processed file: {output_filename}")
            except Exception as e:
                print(f"    ✗ Error saving processed data for interval {interval}: {str(e)}")
                logging.error(f" Error saving processed data for interval {interval}: {str(e)}")
        else:
            print(f"    ✗ No data available for {instrument['tradingsymbol']} {interval}min interval")
            logging.error(f" No data available for {instrument['tradingsymbol']} {interval}min interval")
    return True

# Market open days: Monday (0) to Friday (4)
def is_market_open_day(date=None):
    if date is None:
        date = datetime.date.today()
    return date.weekday() < 5  # 0-4 are Monday-Friday

def look_for_resistant_zones(df_1, df_5, trade_assist_df):
    resistant_zones = get_resistant_zones(df_5, df_1.iloc[-1]['low'], 'Demand')
    if not resistant_zones.empty:
        all_resistant_demand_zones = resistant_zones[resistant_zones['Zones_sup_dem'] == 'Demand']
        # Prepare DataFrame with the required columns
        if not all_resistant_demand_zones.empty:
            new_rows = all_resistant_demand_zones.rename(
                columns={
                    'Zones_sup_dem': 'Resistant_Zone',
                    'low': 'low',
                    'high': 'high',
                    'Zone_Status': 'Zone_Status'
                }
            )[["Resistant_Zone", "low", "high", "Zone_Status"]]
            trade_assist_df = pd.concat([trade_assist_df, new_rows], ignore_index=True)
    
    resistant_zones = get_resistant_zones(df_5, df_1.iloc[-1]['high'], 'Supply')
    if not resistant_zones.empty:
        all_resistant_supply_zones = resistant_zones[resistant_zones['Zones_sup_dem'] == 'Supply']
        # Prepare DataFrame with the required columns
        if not all_resistant_supply_zones.empty:
            new_rows = all_resistant_supply_zones.rename(
                columns={
                    'Zones_sup_dem': 'Resistant_Zone',
                    'low': 'low',
                    'high': 'high',
                    'Zone_Status': 'Zone_Status'
                }
            )[["Resistant_Zone", "low", "high", "Zone_Status"]]
            trade_assist_df = pd.concat([trade_assist_df, new_rows], ignore_index=True)

    return trade_assist_df


def main():
    """
    Main function to run the trading bot.
    """
    logging.info("=== **************** BK EXCLUSIVE ************************ ===")
    logging.info(f"=== ****************START LOGGING* ({datetime.datetime.now().strftime('%H:%M:%S')}) ************ ===")
    logging.info("=== ****************************************************** ===")

    print("=== Algo Trading Bot Started ===")
    print(f"Output for processed NIFTY data: {config.OUTPUT_DIR}")
    print(f"Output for processed Options data: {config.OPTIONS_OUTPUT_DIR}")
    print(f"Options input file: {config.OPTIONS_CSV_FILE}")
    print("="*60)
    
    logging.info("=== Algo Trading Bot Started ===")
    logging.info(f"Output for processed NIFTY data: {config.OUTPUT_DIR}")
    logging.info(f"Output for processed Options data: {config.OPTIONS_OUTPUT_DIR}")
    logging.info(f"Options input file: {config.OPTIONS_CSV_FILE}")
    logging.info("="*60)

    # Initialize data stores and state variables
    first_run = True
    last_15_min_fetch_minute = -1
    
    # State management
    # Possible states: 'SCANNING', 'SIGNAL_FOUND', 'TRADE_READY', 'IN_TRADE'
    trade_state = 'SCANNING' 
    trade_info = {} # To store trade details
    master_dataframes = {}

    trade_log_columns = [
        "Instrument_name",
        "trade_entry_time",
        "trade_exit_time",
        "Trade_entry_price",
        "Trade_exit_price",
        "profit_loss"
    ]
    trade_log_df = pd.DataFrame(columns=trade_log_columns)

    trade_assist_columns = [
        "Resistant_Zone",
        "low",
        "high",
        "Zone_Status"
    ]
    trade_assist_df = pd.DataFrame(columns=trade_assist_columns)

    # Create output directories if they don't exist
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    os.makedirs(config.OPTIONS_OUTPUT_DIR, exist_ok=True)
    os.makedirs(config.OUTPUT_DIR_TRADE_LOG, exist_ok=True)

    df_1, df_5, df_60 = None, None, None
    to_date_str = datetime.datetime.now().strftime('%Y-%m-%d')

    instrument_list = config.instruments_list

    # Message to be sent for telegram bot   
    message_to_send = f"Format ===Algo_Trading===$<Action_on_msg>$<BUY/CLOSE>$<Option Name>$<Entry Price>$<Stop Loss Price>$<Entry Time>"
    #asyncio.run(send_telegram_message(message_to_send))
    message_to_send = f"Example only ===Algo_Trading===$TRADE$BUY$NIFTY 10 JUL 25750 CALL$102.65$98.45$2025-07-02 12:46:00+05:30"
    #asyncio.run(send_telegram_message(message_to_send))

    while True:
        now = datetime.datetime.now()
        #current_time = now.time()
        # 
        # if current_time > config.MARKET_CLOSE:
        #     print(f"→ Market is closed (current time: {current_time}). Exiting...")
        #     logging.info(f"Market is closed (current time: {current_time}). Exiting...")
        #     break
        # if current_time < config.MARKET_START and current_time > config.MARKET_CLOSE:
        #     print(f"→ Market is closed (current time: {current_time}). Waiting...")
        #     logging.info(f"Market is closed (current time: {current_time}). Waiting...")
        #     time.sleep(60)  # Wait for 1 minute before checking again
        #     continue
        # Also capture the saturday and sunday market close and market holiday
        # MARKET_IS_OPEN_TODAY = is_market_open_day(now.date())
        # if MARKET_IS_OPEN_TODAY 
        # if current_time == config.DAY_END:
        #     print(f"→ Market is closed (current time: {current_time}). Exiting...")
        #     logging.info(f"Market is closed (current time: {current_time}). Exiting...")
            #clear the values in user input file and keep the columns name
            # data_handler.clear_user_input_file()
        #     break
        # --- Historical NIFTY Data Processing ---
        print(f"\n--- Processing Historical NIFTY Data ({now.strftime('%H:%M:%S')}) ---")
        logging.info(f"--- Processing Historical NIFTY Data ({now.strftime('%H:%M:%S')}) ---")

        intervals_to_fetch = set()
        if first_run:
            print("→ First run: scheduling all intervals for full historical fetch")
            logging.info(" First run: scheduling all intervals for full historical fetch")
            intervals_to_fetch.update(["1", "5", "60", "daily"])
            first_run = False
        else:
            intervals_to_fetch.update(["1", "5"])

        if now.minute % 15 == 0 and now.minute != last_15_min_fetch_minute:
            print(f"→ Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch")
            logging.info(f" Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch")
            intervals_to_fetch.add("60")
            last_15_min_fetch_minute = now.minute
        
        # with ThreadPoolExecutor(max_workers=len(instrument_list)) as executor:
        #     print(f"Starting ThreadPoolExecutor with {len(instrument_list)} threads")
        #     logging.info(f"Starting ThreadPoolExecutor with {len(instrument_list)} threads")
        #     futures = []
            # for instrument in instrument_list:
            #     print(f"[{threading.current_thread().name}] Submitting job for {instrument['tradingsymbol']}")
            #     logging.info(f"[{threading.current_thread().name}] Submitting job for {instrument['tradingsymbol']}")
            #     future = executor.submit(fetch_and_save_instrument_data, instrument, intervals_to_fetch)
            #     futures.append(future)
            # for future in as_completed(futures):
            #     try:
            #         result = future.result()  # To raise exceptions if any
            #         print(f"[{threading.current_thread().name}] One instrument job completed.")
            #         logging.info(f"[{threading.current_thread().name}] One instrument job completed.")
            #     except Exception as e:
            #         print(f"[{threading.current_thread().name}] Exception in thread: {e}")
            #         logging.error(f"[{threading.current_thread().name}] Exception in thread: {e}")  # To raise exceptions if any
        for instrument in instrument_list: 
            results = []
            for interval in intervals_to_fetch:
                print(f"  → Fetching {instrument['security_id']} {interval}min data...")
                logging.info(f" Fetching {instrument['security_id']} {interval}min data...")
                df, from_date_str, to_date_str = data_handler.fetch_historical_data(security_id=instrument["security_id"], exchange_segment=instrument["exchange_segment"], instrument_type=instrument["instrument_type"], interval=interval)

                if df is not None and not df.empty:
                    print(f"    ✓ Fetched {len(df)} new records")
                    logging.info(f" Fetched {len(df)} new records")
                    
                    df_processed = process_dataframe(df)

                    if interval == "1": 
                        df_1 = df_processed
                    elif interval == "5": 
                        df_5 = df_processed
                    elif interval == "60": 
                        df_60 = df_processed
                    
                    # Save processed file
                    try:
                        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
                        # create directory withrespect to instrument name
                        instrument_dir = os.path.join(config.OUTPUT_DIR, instrument['tradingsymbol'])
                        os.makedirs(instrument_dir, exist_ok=True)
                        # create a day folder with name as current day date then store the .csv inside it. Also check if the folder already exists
                        
                        #today_str = datetime.datetime.now().strftime('%Y-%m-%d')
                        day_dir = os.path.join(instrument_dir, to_date_str)
                        os.makedirs(day_dir, exist_ok=True)
                        # Save CSV inside the day folder
                        interval_suffix = "daily" if interval == "daily" else f"{interval}min"
                        output_filename = f"{instrument['tradingsymbol']}_{interval_suffix}_{from_date_str}_to_{to_date_str}_processed.csv"
                        output_path = os.path.join(day_dir, output_filename)
                        df_processed.to_csv(output_path, index=False)
                        # Append if file exists, else write with header
                        # if os.path.exists(output_path):
                        #     df_processed.to_csv(output_path, mode='a', header=False, index=False)
                        # else:
                        #     df_processed.to_csv(output_path, index=False)
                        print(f"    ✓ Saved processed file: {output_filename}")
                        logging.info(f" Saved processed file: {output_filename}")
                    except Exception as e:
                        print(f"    ✗ Error saving processed data for interval {interval}: {str(e)}")
                        logging.error(f" Error saving processed data for interval {interval}: {str(e)}")
                else:
                    print(f"    ✗ No data available for {instrument['tradingsymbol']} {interval}min interval")
                    logging.error(f" No data available for {instrument['tradingsymbol']} {interval}min interval")

        # --- Trading Logic State Machine ---

        now = datetime.datetime.now()
        current_time = now.time()

        # # demo of find_closest_zones_to_current_candle
        # # current_candle = df_1.iloc[-1]
        # target_ts = pd.Timestamp('2025-07-04 12:09:00+05:30')
        # current_candle = df_1[df_1['timestamp'] == target_ts]
        # if not current_candle.empty:
        #    current_candle = current_candle.iloc[0]
        # closest_zones = find_closest_zones_to_current_candle(df_1, df_5, current_candle)
        # print("closest zones are here for current candle", current_candle)
        # print("$$$$$$$$$$$$$$$$$$$$$$$$$$$----------------------------------------")
        # # usage 1
        # # for idx, row in closest_zones.iterrows():
        # #     print(
        # #         f"Zone Source: {row['zone_source']}, "
        # #         f"Proximity: {row['proximity']}, "
        # #         f"Timestamp: {row.get('timestamp', 'N/A')}, "
        # #         f"Low: {row.get('low', 'N/A')}, "
        # #         f"High: {row.get('high', 'N/A')}"
        # #     )
        
        # # usage 2
        # demand_1min = closest_zones[closest_zones['zone_source'] == '1_min_closer_Demand']
        # if not demand_1min.empty:
        #     row = demand_1min.iloc[0]
        #     print(f"1-min Demand Zone: {row['low']} - {row['high']} at {row['timestamp']}, proximity: {row['proximity']}")
        # print("$$$$$$$$$$$$$$$$$$$$$$$$$$$----------------------------------------")
        # # end-demo
        

        # Example usage of get_resistant_zones function
        
        # Get current price from latest candle
        # current_price = 25565.15
        # option_type = "CALL"
        
        # # Find resistant zones at current price
        # resistant_zones = get_resistant_zones(df_1, current_price, 'Both')
        
        # # Use resistant zones for decision making
        # if not resistant_zones.empty:
        #     logging.info(f"Found {len(resistant_zones)} resistant zones at price {current_price}")
            
        #     # Example: Adjust stop loss based on nearest resistant zone
        #     if option_type == "CALL" and 'Supply' in resistant_zones['Zones_sup_dem'].values:
        #         nearest_supply = resistant_zones[resistant_zones['Zones_sup_dem'] == 'Supply'].iloc[0]
        #         logging.info(f"Found supply zone at {nearest_supply['low']} - {nearest_supply['high']}")
        #         logging.info(f"zone timestamp: {nearest_supply['timestamp']}")
        #         # Adjust stop loss or take other actions
                
        #     elif option_type == "PUT" and 'Demand' in resistant_zones['Zones_sup_dem'].values:
        #         nearest_demand = resistant_zones[resistant_zones['Zones_sup_dem'] == 'Demand'].iloc[0]
        #         logging.info(f"Found demand zone at {nearest_demand['low']} - {nearest_demand['high']}")
        #         logging.info(f"zone timestamp: {nearest_demand['timestamp']}")
        #         # Adjust stop loss or take other actions
        
        # # Continue with existing code...

        trade_assist_df = trade_assist_df.iloc[0:0]
        trade_assist_df = look_for_resistant_zones(df_1, df_5, trade_assist_df)
        # printing all rows of trade_assist_df
        if not trade_assist_df.empty:
            for idx, row in trade_assist_df.iterrows():
                print(f"Resistant zone: {row['Resistant_Zone']} low={row['low']}, high={row['high']}, status={row['Zone_Status']}")

        if trade_state == 'SCANNING' and current_time >= config.START_TRADE and current_time <= config.STOP_TRADE:
            logging.info("State: SCANNING for trade signals.")
            option_type, signal_found = trading_logic.look_for_trade_signals(df_1, df_5, df_60, trade_assist_df, instrument['tradingsymbol'])
            if signal_found == 'True':
                trade_info["option_type"] = option_type
                trade_info["signal_time"] = df_1.iloc[-1]['timestamp'] # Exclusive
                trade_state = 'SIGNAL_FOUND'
                logging.info(f"State change: SCANNING -> SIGNAL_FOUND. Signal for {option_type}.")
        
        if trade_state in ['SIGNAL_FOUND', 'TRADE_READY', 'IN_TRADE']:
            print("\n--- Processing Option Data ---")
            logging.info("--- Processing Option Data ---")
            try:
                options_results = data_handler.process_options_csv(trade_info["option_type"])
                if options_results["success"]:
                    master_dataframes = options_results.get('dataframes', {})
                    logging.info(f"Options processing completed: {options_results['successful_fetches']} successful, {options_results['failed_fetches']} failed")
                else:
                    logging.error(f"Options processing failed: {options_results.get('error', 'Unknown error')}")
                    trade_state = 'SCANNING' # Reset on failure
            except Exception as e:
                logging.error(f"Error during options processing: {str(e)}")
                trade_state = 'SCANNING' # Reset on failure

        if trade_state == 'SIGNAL_FOUND':
            logging.info("State: SIGNAL_FOUND. Getting trade info.")
            trade_symbol, entry_candle = trading_logic.get_info_for_trade(df_1, master_dataframes, trade_info["option_type"], trade_info["signal_time"])
            if trade_symbol is not None and entry_candle is not None:
                if trade_symbol.split()[-1] == trade_info["option_type"]: #Exclusive
                    trade_info['symbol'] = trade_symbol
                    trade_info['entry_candle'] = entry_candle
                    trade_state = 'TRADE_READY'
                    logging.info(f"State change: SIGNAL_FOUND -> TRADE_READY for {trade_symbol}.")
                else: #Exclusive
                    logging.info(f"Crossover not happened in option yet for {trade_info['option_type']}, So waiting...") #Exclusive
                    trade_state = 'SIGNAL_FOUND' #Exclusive
            else:
                logging.warning(f"Could not find a tradable option for {trade_info['option_type']}. Resetting.")
                trade_state = 'SCANNING'

        if trade_state == 'TRADE_READY':
            logging.info(f"State: TRADE_READY. Attempting to take trade for {trade_info['symbol']}.")
            entry_time, trade_status = trading_logic.take_the_trade(
                trade_info['symbol'], trade_info['entry_candle'], master_dataframes
            )
            if trade_status == "Trade Taken":
                trade_info['entry_time'] = entry_time
                trade_info['entry_price'] = master_dataframes[trade_info['symbol']]['df_opt_1min'].iloc[-1]['low']

                Option_1min_crossover_and_zone = find_latest_signals(master_dataframes[trade_info['symbol']]['df_opt_1min'])
                trade_info['opt_1min_latest_Demand'] = Option_1min_crossover_and_zone['latest_zone']
                trade_info['stop_loss'] = Option_1min_crossover_and_zone['latest_zone']['low']
                trade_info['opt_1min_latest_Supply'] = Option_1min_crossover_and_zone['prior_to_latest_zone']
                Option_5min_crossover_and_zone = find_latest_signals(master_dataframes[trade_info['symbol']]['df_opt_5min'])
                trade_info['opt_5min_latest_Demand'] = Option_5min_crossover_and_zone['latest_zone']
                trade_info['opt_5min_latest_Supply'] = Option_5min_crossover_and_zone['prior_to_latest_zone']
                
                # Message to be sent for telegram bot   
                message_to_send = f"===Algo_Trading===$TRADE$BUY${trade_info['symbol']}${trade_info['entry_price']}${trade_info['stop_loss']}${trade_info['entry_time']}"
                #asyncio.run(send_telegram_message(message_to_send))

                new_row = {"Instrument_name": trade_info['symbol'], "trade_entry_time": trade_info['entry_time'], "Trade_entry_price": trade_info['entry_price']}
                trade_log_df = pd.concat([trade_log_df, pd.DataFrame([new_row])], ignore_index=True)
                
                trade_state = 'IN_TRADE'
                logging.info(f"State change: TRADE_READY -> IN_TRADE. Trade taken for {trade_info['symbol']} at {trade_info['entry_price']}.")
                logging.info(f"State change: TRADE_READY -> IN_TRADE. Trade taken for {trade_info['symbol']} at {trade_info['entry_price']}.")
                logging.info(f"Latest option 1 min demand : {trade_info['opt_1min_latest_Demand']['timestamp']}")
                logging.info(f"Latest option 1 min supply : {trade_info['opt_1min_latest_Supply']['timestamp']}")
                logging.info(f"Stop loss is initially set to : {trade_info['stop_loss']}")
            elif trade_status == "False":
                logging.info(f"Trade invalidated for {trade_info['symbol']}. Resetting.")
                trade_state = 'SCANNING'

        if trade_state == 'IN_TRADE':
            logging.info(f"State: IN_TRADE. Monitoring trade for {trade_info['symbol']}.")
            exit_status, trade_info['stop_loss'] = trading_logic.check_for_trade_exit(
                df_1, df_5, trade_info['symbol'], trade_info['entry_time'], master_dataframes, trade_log_df, trade_assist_df, trade_info['opt_1min_latest_Demand'],
                trade_info['opt_1min_latest_Supply'], trade_info['opt_5min_latest_Demand'], trade_info['opt_5min_latest_Supply'], trade_info['stop_loss']
            )
            if exit_status == 'Trade Exit':
                logging.info(f"Exit signal received for {trade_info['symbol']}. Exiting trade.")
                trading_logic.exit_the_trade(
                    trade_info['symbol'], trade_info['entry_time'], master_dataframes, trade_log_df, to_date_str
                )
                trade_state = 'SCANNING'
                trade_info = {} # Reset trade info
                logging.info("State change: IN_TRADE -> SCANNING. Trade exited.")

        print(f"\n{'='*60}")
        print(f"Current State: {trade_state}. Next update in {config.FETCH_INTERVAL_SECONDS} seconds...")
        print(f"{'='*60}\n")
        logging.info(f"{'='*60}")
        logging.info(f"Current State: {trade_state}. Next update in {config.FETCH_INTERVAL_SECONDS} seconds...")
        logging.info(f"{'='*60}\n")

        if current_time > config.MARKET_CLOSE:
            print(f"→ Market is closed (current time: {current_time}). Exiting...")
            logging.info(f"Market is closed (current time: {current_time}). Exiting...")
            break
        if current_time < config.MARKET_START:
            time.sleep(config.PRE_MARKET_INTERVAL_SECONDS)
        else:
            time.sleep(config.FETCH_INTERVAL_SECONDS)

if __name__ == "__main__":
    main()
